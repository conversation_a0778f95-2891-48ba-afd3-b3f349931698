package com.yy.hdzt.ranking.service;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yy.hdzt.common.model.hdzt.RankingConfig;
import com.yy.hdzt.common.model.hdzt.RankingPhase;
import com.yy.hdzt.common.model.hdzt.RankingTask;
import com.yy.hdzt.common.model.hdzt.RankingTaskItem;
import com.yy.hdzt.common.thrift.hdzt.ranking.QueryUserTaskRequest;
import com.yy.hdzt.common.thrift.hdzt.ranking.QueryUserTaskResponse;
import com.yy.hdzt.common.thrift.hdzt.ranking.RankingTaskItemResponse;
import com.yy.hdzt.common.thrift.hdzt.ranking.UserTaskItem;
import com.yy.hdzt.common.utils.Convert;
import com.yy.hdzt.common.utils.DateUtil;
import com.yy.hdzt.common.utils.StringUtil;
import com.yy.hdzt.ranking.dao.redis.RankingRedisDao;
import com.yy.hdzt.ranking.support.TimeKeyHelper;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.data.redis.connection.RedisConnection;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * desc:
 *
 * @createBy 曾文帜
 * @create 2020-07-30 20:39
 **/
@Service
public class TaskInfoService {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private CacheService cacheService;

    @Autowired
    private RankingRedisDao rankingRedisDao;

    @Autowired
    private KeyService keyService;

    @Autowired
    private RedisGroupService redisGroupService;


    /**
     *        hdzt_ranking:202008001:120:_:_:_|0.task.${itemId}  - 过任务的项目采集数量 key
     *
     *        hash:
     *        hdzt_ranking:202008001:120:info
     *
     *        field:
     *        ${member}_${yyyyMMdd}_reset_count - 完成多少轮（0开始）
     *
     *        ${member}_0_${yyyyMMdd}_last_task - 当前处于哪关
     */
    public QueryUserTaskResponse queryUserTask(QueryUserTaskRequest request) {

        Long actId = request.getActId();
        Long rankId = request.getRankId();
        Long phaseId = request.getPhaseId();
        String member = request.getMemberId();
        String dateStr = request.getDateStr();

        //---参数校验
        QueryUserTaskResponse response = verifyInput(actId, rankId, phaseId, dateStr);
        if(!StringUtil.isBlank(response.getReason())){
            return response;
        }

        //---组件查询过任务的key
        List<RankingTaskItem> rankingTaskItems = cacheService.getRankingTaskItemList(actId, rankId, phaseId);
        List<String> itemIds = rankingTaskItems.stream().map(RankingTaskItem::getItemId).distinct().toList();
        List<String> scoreKeys = Lists.newArrayList();
        RankingConfig rankingConfig = cacheService.getRankingConfig(actId, rankId);
        if (rankingConfig == null) {
            return response;
        }
        Date now = TimeKeyHelper.fromTimeCode(rankingConfig.getTimeKey(), dateStr);
        for (String item : itemIds) {
            scoreKeys.add(makeCurScoreKey(actId, rankId, phaseId, now, item));
        }

        //---redis查询过当前过的任务
        String hashKey = keyService.makeRankInfoDefaultKey(actId, rankId, now);

        String completeRoundField = makeUserCompleteRoundField(dateStr, phaseId, member);
        String curRoundField = makeUserCurRoundField(phaseId, dateStr, member);

        long group = redisGroupService.getGroupCode(actId, rankId);

        int taskUseHash = cacheService.findActivityConfigExt(actId).getTask_use_hash();
        List<Object> datas = rankingRedisDao.getRedisTemplate(group).execute(new RedisCallback<List<Object>>() {
            @Override
            public List<Object> doInRedis(RedisConnection connection) throws DataAccessException {
                connection.openPipeline();
                //礼物当前阶段积分
                for (int i = 0; i < scoreKeys.size(); ++i) {
                    if(taskUseHash == 1) {
                        connection.hGet(scoreKeys.get(i).getBytes(), member.getBytes());
                    } else {
                        connection.zScore(scoreKeys.get(i).getBytes(), member.getBytes());
                    }
                }
                //已完成总轮数
                connection.hGet(hashKey.getBytes(), completeRoundField.getBytes());

                //当前所在关卡
                connection.hGet(hashKey.getBytes(), curRoundField.getBytes());

                return connection.closePipeline();
            }
        });


        List<RankingTask> rankingTaskList = cacheService.getRankingTaskList(actId, rankId, phaseId);
        //---礼物当前阶段积分
        Map<String, UserTaskItem> userTaskItemMap = Maps.newHashMap();
        for (int i = 0; i < itemIds.size(); i++) {
            UserTaskItem userTaskItem = new UserTaskItem();
            Double curTaskScore = Double.NaN;
            Object data = datas.get(i);
            if (data != null) {
                if(taskUseHash == 1) {
                    curTaskScore = Convert.toDouble(new String((byte[]) data), 0);
                } else {
                    curTaskScore = (Double) data;
                }
            }
            userTaskItem.setAllTaskCount(rankingTaskList.size());
            userTaskItem.setCurTaskScore(curTaskScore.longValue());
            userTaskItemMap.put(itemIds.get(i), userTaskItem);
        }

        //----当前轮总轮数
        long curRound = 0;
        Object curRoundData = datas.get(itemIds.size());
        if (curRoundData != null) {
            curRound = Convert.toLong(new String((byte[]) curRoundData), 0);
        }
        //当前正在过关轮数
        curRound = curRound + 1;


        //---计算当前所在关卡是哪一关
        long curTaskId = 0;
        Object curTaskIdIndexObj = datas.get(itemIds.size() + 1);
        if (curTaskIdIndexObj != null) {
            int curTaskIdIndex = Convert.toInt(new String((byte[]) curTaskIdIndexObj), 0);
            RankingTask curTask = null;
            //已经完成最后一关
            if (curTaskIdIndex >= rankingTaskList.size() - 1) {
                curTask = rankingTaskList.get(rankingTaskList.size() - 1);
                curTaskId = curTask.getTaskId();
            } else {
                //当前关 = 已经完成关的下一关,index从0开始，所以这里不用+1
                curTask = rankingTaskList.get(curTaskIdIndex);
                curTaskId = curTask.getTaskId();
            }
        } else {
            if (rankingTaskList.size() > 0) {
                curTaskId = rankingTaskList.get(0).getTaskId();
            }
        }


        //----计算单个item完成所有关所需要积分,给后面用  key 礼物id value
        Map<String, Long> curTaskPassScoreConfig = Maps.newHashMap();
        Map<String, Long> totalTaskPassScoreConfig = Maps.newHashMap();
        for (RankingTaskItem rankingTaskItem : rankingTaskItems) {
            //itemId + "_" + curTaskId
            String key = rankingTaskItem.getItemId() + "_" + curTaskId;
            Long proTotal = curTaskPassScoreConfig.getOrDefault(key, 0L);
            if (proTotal >= 0) {
                proTotal = proTotal + rankingTaskItem.getPassValue();
                //已经累到当前阶段，不再累加,用负数标记
                if (rankingTaskItem.getTaskId() == curTaskId) {
                    proTotal = -1 * proTotal;
                }
                curTaskPassScoreConfig.put(key, proTotal);
            }

            String totalTaskKey = rankingTaskItem.getItemId();
            Long passScore = totalTaskPassScoreConfig.getOrDefault(totalTaskKey, 0L);
            totalTaskPassScoreConfig.put(totalTaskKey, passScore + rankingTaskItem.getPassValue());
        }


        //---组织返回数据
        for (String itemId : userTaskItemMap.keySet()) {
            UserTaskItem userTaskItem = userTaskItemMap.get(itemId);
            userTaskItem.setCurTaskId(curTaskId);
            userTaskItem.setCurRound(curRound);
            userTaskItem.setItemId(itemId);
            userTaskItem.setItemName(cacheService.getHdztGiftName(actId, itemId));
            userTaskItem.setItemUrl(cacheService.getHdztGiftUrl(actId, itemId));
            userTaskItem.setItemScore(cacheService.getHdztGiftScore(actId,itemId));

            //任务名称
            RankingTask rankingTask = cacheService.getRankingTaskInfo(actId,rankId,phaseId,curTaskId);
            if (rankingTask != null) {
                userTaskItem.setTaskName(rankingTask.getTaskName());
                userTaskItem.setTaskExtJson(rankingTask.getExtjson());
            }

            //完成当前关所需要的积分
            String key = itemId + "_" + curTaskId;
            Long passScore = curTaskPassScoreConfig.getOrDefault(key, 0L);
            passScore = passScore * -1;
            userTaskItem.setCurTaskScoreConfig(passScore);

            //当前项目完成所有关所需要的的积分
            Long passTotalTaskScore = totalTaskPassScoreConfig.getOrDefault(itemId, 0L);
            userTaskItem.setAllTaskScoreConfig(passTotalTaskScore);
        }

        response.setCurTaskId(curTaskId);
        response.setCurRound(curRound);
        response.setItems(userTaskItemMap);
        return response;

    }

    public RankingTaskItemResponse queryRankingTaskItem(Long actId, Long rankId, Long phaseId) {
        RankingTaskItemResponse response = new RankingTaskItemResponse();
        List<com.yy.hdzt.common.thrift.hdzt.ranking.RankingTaskItem> itemResult = Lists.newArrayList();

        List<RankingTaskItem> rankingTaskItems = null;
        if (phaseId == 0) {
            rankingTaskItems = cacheService.getRankingTaskItemList(actId, rankId);
        } else {
            rankingTaskItems = cacheService.getRankingTaskItemList(actId, rankId, phaseId);
        }
        if (!CollectionUtils.isEmpty(rankingTaskItems)) {
            rankingTaskItems.forEach(x -> {
                com.yy.hdzt.common.thrift.hdzt.ranking.RankingTaskItem item = new com.yy.hdzt.common.thrift.hdzt.ranking.RankingTaskItem();
                item.setItemId(x.getItemId());
                item.setExtjson(x.getExtjson());
                item.setPassValue(x.getPassValue());
                item.setRemark(x.getRemark());
                item.setTaskId(x.getTaskId());
                item.setWeight(x.getWeight());
                itemResult.add(item);
            });

        }

        response.setItems(itemResult);
        return response;

    }

    private QueryUserTaskResponse verifyInput(Long actId, Long rankId, Long phaseId, String dateStr) {
        QueryUserTaskResponse response = new QueryUserTaskResponse();
        response.setCode(0);
        response.setItems(Maps.newHashMap());


        //---参数校验
        RankingConfig rankingConfig = cacheService.getRankingConfig(actId, rankId);
        if (rankingConfig == null) {
            response.setReason("榜单不存在");
            return response;
        }
        RankingPhase rankingPhase = cacheService.getRankingPhase(actId, phaseId);
        if (rankingPhase == null) {
            response.setReason("阶段不存在");
            return response;
        }

        Date showEndTime = rankingConfig.getShowEndTime();
        //目前只支持日榜，后面可以优化
        Date now = DateUtil.getDateByDateStr(dateStr);
        if (now != null) {
            if (now.getTime() > showEndTime.getTime()) {
                response.setReason("已结束");
                return response;
            }
        }

        return response;
    }


    //当前阶段积分
    private String makeCurScoreKey(Long actId, Long rankingId, Long phaseId, Date time, String itemId) {
        //准备key的前缀
        String actRankKey = keyService.getActRankKey(actId, rankingId, phaseId, "", time, "");
        //_:_:_|${phaseId}.task.${itemId}
        return String.format(actRankKey + ".task.%s", itemId);
    }


    //完成轮数 field
    private String makeUserCompleteRoundField(String dateStr, Long phaseId, String member) {
        return String.format("%s_%s_reset_count", member, phaseId);
    }

    //当前所处关数 field
    private String makeUserCurRoundField(Long phaseId, String dateStr, String member) {
        return String.format("%s_%s_last_task", member, phaseId);
    }
}
