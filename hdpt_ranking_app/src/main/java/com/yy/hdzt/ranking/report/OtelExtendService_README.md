# OtelExtendService 使用说明

## 概述

`OtelExtendService` 是一个 OpenTelemetry 扩展服务，用于在 otel 上下文中设置 `traceBeginTime` 和 `scene` 信息，并传递给下游系统。

## 功能特性

1. **全局开关控制**：通过 `ParameterManager` 控制功能的启用/禁用
2. **自动上下文设置**：从 `BaseEvent` 自动提取时间戳和场景信息
3. **容错处理**：开关关闭或出错时不影响原有业务逻辑
4. **工具类支持**：提供底层的 otel 上下文操作方法

## 配置

### 全局开关

在 `sys_parameter` 表中添加以下配置：

```sql
INSERT INTO sys_parameter (CLAZZ, NAME, VALUE, ALIAS, SHWORD) 
VALUES ('', 'otel_extend_enable', 'true', '启用otel扩展功能', 1);
```

- `otel_extend_enable`: 参数名
- `true`/`false` 或 `1`/`0`: 启用/禁用
- 默认值：`false`（关闭）

## 使用方式

### 1. 在 EventNotifyService 中的集成

已经在 `EventNotifyService.sendMessage()` 方法中集成，会自动：
- 从 `BaseEvent.timestamp` 获取 `traceBeginTime`
- 从 `BaseEvent` 的实际类名获取 `scene`
- 设置到 otel 上下文中并传递给下游

### 2. 直接使用 OtelExtendService

```java
@Autowired
private OtelExtendService otelExtendService;

// 方式1：使用回调
BaseEvent event = new ActivityTimeStart();
event.setTimestamp("2025-08-05 16:30:00");

otelExtendService.setOtelContextForEvent(event, () -> {
    // 在这里执行需要传递上下文的业务逻辑
    // traceBeginTime 和 scene 已经设置到 otel 上下文中
    someDownstreamService.call();
});

// 方式2：获取 Context 自己管理
Context context = otelExtendService.setOtelContextForEvent(event);
try (Scope scope = context.makeCurrent()) {
    // 在这里执行需要传递上下文的业务逻辑
    someDownstreamService.call();
}
```

### 3. 直接使用 OtelExtendTool 工具类

```java
// 设置单个值
Context context1 = OtelExtendService.OtelExtendTool.setTraceBeginTime(System.currentTimeMillis());
Context context2 = OtelExtendService.OtelExtendTool.setScene("ActivityTimeStart");

// 同时设置两个值
Context context = OtelExtendService.OtelExtendTool.setTraceBeginTimeAndScene(
    System.currentTimeMillis(), 
    "ActivityTimeStart"
);

// 使用便利方法
OtelExtendService.OtelExtendTool.runWithTraceBeginTimeAndScene(
    System.currentTimeMillis(), 
    "ActivityTimeStart", 
    () -> {
        // 业务逻辑
    }
);

// 获取当前上下文中的值
String traceBeginTime = OtelExtendService.OtelExtendTool.getTraceBeginTime();
String scene = OtelExtendService.OtelExtendTool.getScene();
long traceBeginTimeLong = OtelExtendService.OtelExtendTool.getTraceBeginTimeLong();
```

## 上下文传播

设置的 `traceBeginTime` 和 `scene` 会通过 OpenTelemetry 的 Baggage 机制自动传播到：

1. **同一线程内的后续调用**
2. **跨服务的 HTTP 调用**（如果配置了 otel 的 HTTP 传播）
3. **异步任务**（如果正确传递了 Context）
4. **消息队列**（如果配置了相应的传播机制）

## 日志输出

当功能启用时，会输出以下调试日志：

```
DEBUG - Set otel context for event: scene=ActivityTimeStart, traceBeginTime=2025-08-05 16:30:00
```

当功能禁用或出错时，会输出相应的警告或错误日志。

## 注意事项

1. **性能影响**：功能关闭时对性能无影响，启用时有轻微的上下文设置开销
2. **容错设计**：任何异常都不会影响原有业务逻辑的执行
3. **线程安全**：所有方法都是线程安全的
4. **内存使用**：Baggage 数据会随着 Context 传播，注意不要设置过大的值

## 扩展

如需添加更多上下文信息，可以在 `OtelExtendTool` 中添加新的键值对：

```java
private static final String NEW_KEY = "newKey";

public static Context setNewValue(String value) {
    Baggage currentBaggage = Baggage.fromContext(Context.current());
    Baggage newBaggage = currentBaggage.toBuilder()
            .put(NEW_KEY, value)
            .build();
    return Context.current().with(newBaggage);
}
```
