/**
 * EventNotifyService.java / 2020年7月29日 下午6:47:10
 * <p>
 * Copyright (c) 2020, YY Inc. All Rights Reserved.
 * <p>
 * 郭立平[<EMAIL>]
 */
package com.yy.hdzt.ranking.service;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.yy.hdzt.common.consts.Const;
import com.yy.hdzt.common.consts.ParamName;
import com.yy.hdzt.common.model.hdzt.*;
import com.yy.hdzt.common.notify.BaiduInfoFlowRobotService;
import com.yy.hdzt.common.support.SysEvHelper;
import com.yy.hdzt.common.utils.*;
import com.yy.hdzt.ranking.bean.*;
import com.yy.hdzt.ranking.commons.HdztConst;
import com.yy.hdzt.ranking.dao.mysql.RankingMysqlDao;
import com.yy.hdzt.ranking.dao.redis.RankingRedisDao;
import com.yy.hdzt.ranking.event.*;
import com.yy.hdzt.ranking.support.TimeKeyHelper;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.TimeUnit;

/**
 * 活动、榜单、阶段等开始、结束的时间相关事件服务处理
 *
 * <AUTHOR>
 * @date 2020年7月29日 下午6:47:10 
 */
@Service
public class EventNotifyService {

    private Logger log = LoggerFactory.getLogger(EventNotifyService.class);

    @Autowired
    private CacheService cacheService;

    @Value("${kafka.hdzt.ranking.events.topic}")
    private String rankingEventsTopic;

    @Autowired
    @Qualifier("hdztWxKafkaTemplate")
    private KafkaTemplate<String, String> hdztWxKafkaTemplate;

    @Autowired
    @Qualifier("hdztSzKafkaTemplate")
    private KafkaTemplate<String, String> hdztSzKafkaTemplate;

    @Autowired
    private RankingRedisDao rankingRedisDao;

    @Autowired
    private RankingMysqlDao rankingMysqlDao;

    @Autowired
    TaskAwardIssueService taskAwardIssueService;

    @Autowired
    private HdztActivityService hdztActivityService;

    @Autowired
    private RedisGroupService redisGroupService;

    @Autowired
    private BaiduInfoFlowRobotService baiduInfoFlowRobotService;

    @Autowired
    private com.yy.hdzt.ranking.report.OtelExtendService otelExtendService;

    // 消息RabbitMQ消息失败时，暂时存放，重试发送（双MQ+内存暂存失败消息重试 已经非常可靠了，特意不用redis外部存储失败消息的复杂方案）
    private Queue<Map<String, Object>> eventNotifyFailQueue = new ConcurrentLinkedQueue<Map<String, Object>>();

    public static final String TIME_EVENT_NOTIFY = "EventNotify";

    /**
     * 发送活动开始事件（防重复）
     * <AUTHOR>
     * @date 2020年7月30日 下午3:42:49 
     */
    public void sendActivityStartEvent(Date now, HdztActivity activity) {
        Clock clock = new Clock();
        ActivityTimeStart event = new ActivityTimeStart();
        long times = -1;
        try {
            Long actId = activity.getActId();
            String ekey = String.format("[%s]ActivityTimeStart#%s", event.getUri(), actId);
            String timestamp = DateUtil.format(now);
            times = saveSendMsgFlag(actId, ekey, DateUtil.format(now) + " -> " + DateUtil.format(activity.getBeginTime()));
            if (needSendMessage(times)) {
                event.setActId(actId);
                event.setStartTime(DateUtil.format(activity.getBeginTime()));
                event.setTimestamp(timestamp);
                event.setEkey(ekey);
                event.setIndex(times);
                sendMessage(event, "ActivityTimeStart:" + times);
            }
        } catch (Throwable t) {
            log.error("sendStartEvent exception@times:{}, event:{}, err:{} {}", times, event, t.getMessage(), clock.tag(), t);
        }
    }

    private boolean needSendMessage(long times) {
        if (!SysEvHelper.isDeploy()) {
            return times <= 10 ? true : times % 10 == 0;
        }
        // 定时器频率1秒检查一次，持续15分钟，某个事件出现的前10次即刻通知，2~101次每10秒通知，之后每60秒通知
        return times <= 10 || (times <= 100 && times % 10 == 0) || (times > 100 && times % 60 == 0);
    }

    /**
     * 发送活动结束事件（防重复）
     * <AUTHOR>
     * @date 2020年7月30日 下午3:43:01 
     */
    public void sendActivityEndEvent(Date now, HdztActivity activity) {
        Clock clock = new Clock();
        ActivityTimeEnd event = new ActivityTimeEnd();
        long times = -1;
        try {
            Long actId = activity.getActId();
            String ekey = String.format("[%s]ActivityTimeEnd#%s", event.getUri(), actId);
            String timestamp = DateUtil.format(now);
            times = saveSendMsgFlag(actId, ekey, DateUtil.format(now) + " -> " + DateUtil.format(activity.getEndTime()));
            if (needSendMessage(times)) {
                event.setActId(actId);
                event.setEndTime(DateUtil.format(activity.getEndTime()));
                event.setTimestamp(timestamp);
                event.setEkey(ekey);
                event.setIndex(times);
                sendMessage(event, "ActivityTimeEnd:" + times);
            }
        } catch (Throwable t) {
            log.error("sendEndEvent exception@times:{}, event:{}, err:{} {}", times, event, t.getMessage(), clock.tag(), t);
        }
    }


    /**
     * 发送榜单开始事件（防重复）
     * <AUTHOR>
     * @date 2020年7月30日 下午3:43:31 
     */
    public void sendRankingStartEvent(Date now, RankingConfig ranking, Date beginTime) {
        Clock clock = new Clock();
        RankingTimeStart event = new RankingTimeStart();
        long times = -1;
        try {
            long actId = ranking.getActId();
            Long timeKey = ranking.getTimeKey();
            String ekey = String.format("[%s]RankingTimeStart#%s:%s:%s", event.getUri(), actId, ranking.getRankId(), TimeKeyHelper.getTimeSubKey(timeKey, beginTime));
            String timestamp = DateUtil.format(now);
            times = saveSendMsgFlag(actId, ekey, DateUtil.format(now) + " -> " + DateUtil.format(beginTime));
            if (needSendMessage(times)) {
                event.setActId(ranking.getActId());
                event.setRankId(ranking.getRankId());
                event.setTimeKey(timeKey);
                event.setStartTime(DateUtil.format(beginTime));
                event.setTimestamp(timestamp);
                event.setEkey(ekey);
                event.setIndex(times);
                sendMessage(event, "RankingTimeStart:" + times);
            }
        } catch (Throwable t) {
            log.error("sendStartEvent exception@times:{}, event:{}, err:{} {}", times, event, t.getMessage(), clock.tag(), t);
        }
    }

    /**
     * 发送榜单结束事件（防重复）
     * <AUTHOR>
     * @date 2020年7月30日 下午3:43:43 
     */
    public void sendRankingEndEvent(Date now, RankingConfig ranking, Date endTime) {
        Clock clock = new Clock();
        RankingTimeEnd event = new RankingTimeEnd();
        long times = -1;
        try {
            long actId = ranking.getActId();
            Long timeKey = ranking.getTimeKey();
            String ekey = String.format("[%s]RankingTimeEnd#%s:%s:%s", event.getUri(), actId, ranking.getRankId(), TimeKeyHelper.getTimeSubKey(timeKey, endTime));
            String timestamp = DateUtil.format(now);
            times = saveSendMsgFlag(actId, ekey, DateUtil.format(now) + " -> " + DateUtil.format(endTime));
            if (needSendMessage(times)) {
                event.setActId(ranking.getActId());
                event.setRankId(ranking.getRankId());
                event.setTimeKey(ranking.getTimeKey());
                event.setEndTime(DateUtil.format(endTime));
                event.setTimestamp(timestamp);
                event.setEkey(ekey);
                event.setIndex(times);
                sendMessage(event, "RankingTimeEnd:" + times);
            }
        } catch (Throwable t) {
            log.error("sendEndEvent exception@times:{}, event:{}, err:{} {}", times, event, t.getMessage(), clock.tag(), t);
        }
    }

    /**
     * 发送榜单阶段开始事件（防重复）
     * <AUTHOR>
     * @date 2020年7月30日 下午4:12:27 
     */
    public void sendPhaseStartEvent(Date now, RankingConfig ranking, RankingPhase phase, Date phaseBeginTime) {
        Clock clock = new Clock();
        PhaseTimeStart event = new PhaseTimeStart();
        long times = -1;
        try {
            long actId = ranking.getActId();
            Long timeKey = ranking.getTimeKey();
            String ekey = String.format("[%s]PhaseTimeStart#%s:%s:%s:%s", event.getUri(), actId, ranking.getRankId(), phase.getPhaseId(), TimeKeyHelper.getTimeSubKey(timeKey, phaseBeginTime));
            String timestamp = DateUtil.format(now);
            times = saveSendMsgFlag(actId, ekey, DateUtil.format(now) + " -> " + DateUtil.format(phaseBeginTime));
            if (needSendMessage(times)) {
                event.setActId(ranking.getActId());
                event.setRankId(ranking.getRankId());
                event.setPhaseId(phase.getPhaseId());
                event.setTimeKey(timeKey);
                event.setStartTime(DateUtil.format(phaseBeginTime));
                event.setTimestamp(timestamp);
                event.setEkey(ekey);
                event.setIndex(times);
                sendMessage(event, "PhaseTimeStart:" + times);
            }
        } catch (Throwable t) {
            log.error("sendStartEvent exception@times:{}, event:{}, err:{} {}", times, event, t.getMessage(), clock.tag(), t);
        }
    }

    /**
     * 发送榜单阶段结束事件（防重复）
     * <AUTHOR>
     * @date 2020年7月30日 下午4:12:48 
     */
    public void sendPhaseEndEvent(Date now, RankingConfig ranking, RankingPhase phase, Date phaseEndTime) {
        Clock clock = new Clock();
        PhaseTimeEnd event = new PhaseTimeEnd();
        long times = -1;
        try {
            long actId = ranking.getActId();
            Long timeKey = ranking.getTimeKey();
            String ekey = String.format("[%s]PhaseTimeEnd#%s:%s:%s:%s", event.getUri(), actId, ranking.getRankId(), phase.getPhaseId(), TimeKeyHelper.getTimeSubKey(timeKey, phaseEndTime));
            String timestamp = DateUtil.format(now);
            times = saveSendMsgFlag(actId, ekey, DateUtil.format(now) + " -> " + DateUtil.format(phaseEndTime));
            if (needSendMessage(times)) {
                event.setActId(ranking.getActId());
                event.setRankId(ranking.getRankId());
                event.setPhaseId(phase.getPhaseId());
                event.setTimeKey(timeKey);
                event.setEndTime(DateUtil.format(phaseEndTime));
                event.setTimestamp(timestamp);
                event.setEkey(ekey);
                event.setIndex(times);
                sendMessage(event, "PhaseTimeEnd:" + times);
            }
        } catch (Throwable t) {
            log.error("sendEndEvent exception@times:{}, event:{}, err:{} {}", times, event, t.getMessage(), clock.tag(), t);
        }
    }

    /**
     * 发送PK结算结束消息（防重复）
     * <AUTHOR>
     * @date 2021年3月23日 上午10:55:32
     */
    public void sendPkSettleEndEvent(Date now, List<PkSettleInfo> settleActDataInfos) {
        int inx = 0;
        String timestamp = DateUtil.format(now);
        for (PkSettleInfo settleActDataInfo : settleActDataInfos) {
            inx++;
            Clock clock = new Clock();
            PkSettleTimeEnd event = new PkSettleTimeEnd();
            long times = -1;
            try {
                String promotKey = settleActDataInfo.getActPhaseRankPromotKey();
                String pkSettleKey = settleActDataInfo.getActPhaseRankPkSettleKey();
                long actId = settleActDataInfo.getActId();
                String ekey = String.format("[%s]PkSettleTimeEnd#%s", event.getUri(), pkSettleKey);
                times = saveSendMsgFlag(actId, ekey, DateUtil.format(now) + " -> " + DateUtil.format(now));
                if (needSendMessage(times)) {
                    long phaseId = settleActDataInfo.getPhaseId();
                    long rankId = settleActDataInfo.getRankId();
                    RankingPhase phase = cacheService.getRankingPhase(actId, phaseId);
                    Long timeKey = cacheService.getRankingConfig(actId, rankId).getTimeKey();
                    event.setDay(settleActDataInfo.getDay());
                    event.setActId(actId);
                    event.setRankId(rankId);
                    event.setPhaseId(phaseId);
                    event.setTimeKey(timeKey);
                    event.setEndTime(DateUtil.format(phase.getEndTime()));
                    event.setTimestamp(timestamp);
                    event.setPromotKey(promotKey);
                    event.setPkSettleKey(pkSettleKey);
                    event.setEkey(ekey);
                    event.setIndex(times);
                    sendMessage(event, "PkSettleTimeEnd:" + times);
                }
            } catch (Throwable t) {
                log.error("sendEndEvent exception@inx:{}, times:{}, event:{}, err:{} {}", inx, times, event, t.getMessage(), clock.tag(), t);
            }
        }
    }

    /**
     * 发送晋级结算结束消息（防重复）
     * <AUTHOR>
     * @date 2020年7月29日 下午7:47:52 
     */
    public void sendPromotEndEvent(Date now, List<PromotSettleInfo> settleActDataInfos) {
        int inx = 0;
        String timestamp = DateUtil.format(now);
        for (PromotSettleInfo settleActDataInfo : settleActDataInfos) {
            inx++;
            Clock clock = new Clock();
            PromotTimeEnd event = new PromotTimeEnd();
            long times = -1;
            try {
                String promotKey = settleActDataInfo.getActPhaseRankPromotKey();
                long actId = settleActDataInfo.getActId();
                String ekey = String.format("[%s]PromotTimeEnd#%s", event.getUri(), promotKey);
                times = saveSendMsgFlag(actId, ekey, DateUtil.format(now) + " -> " + DateUtil.format(now));
                if (needSendMessage(times)) {
                    long phaseId = settleActDataInfo.getPhaseId();
                    long rankId = settleActDataInfo.getRankId();
                    RankingPhase phase = cacheService.getRankingPhase(actId, phaseId);
                    Long timeKey = cacheService.getRankingConfig(actId, rankId).getTimeKey();
                    event.setActId(actId);
                    event.setRankId(rankId);
                    event.setPhaseId(phaseId);
                    event.setTimeKey(timeKey);
                    event.setEndTime(DateUtil.format(phase.getEndTime()));
                    event.setTimestamp(timestamp);
                    event.setPromotKey(promotKey);
                    event.setEkey(ekey);
                    event.setIndex(times);
                    sendMessage(event, "PromotTimeEnd:" + times);
                }
            } catch (Throwable t) {
                log.error("sendEndEvent exception@inx:{}, times:{}, event:{}, err:{} {}", inx, times, event, t.getMessage(), clock.tag(), t);
            }
        }
    }

    /**
     * 通知阶段任务改变
     * <AUTHOR>
     * @date 2020年7月30日 下午12:43:22 
     */
    public void notifyPhaseTaskChange(RankDataEvent dataEvent, String seq, Map<Long, RankingUpdateResult> result) {
        int inx = 0;
        long actId = dataEvent.getActId();
        String timestamp = DateUtil.format(hdztActivityService.getCurrSysTime(actId));
        for (Long rankId : result.keySet()) {
            inx++;
            Clock clock = new Clock();
            RankingUpdateResult rankingUpdateResult = result.get(rankId);
            try {
                PhaseTaskResult phaseTaskResult = rankingUpdateResult.getPhaseTaskResult();
                if (phaseTaskResult == null) {
                    continue;
                }
                if (phaseTaskResult.isStationChanged()) {
                    BaseEvent event =  phaseTaskResult.getRecycleType() == HdztConst.TASK_RECYCLE_TYPE_BY_ALL ?
                            makeTaskProgressChanged(dataEvent, actId, timestamp, rankId, rankingUpdateResult, phaseTaskResult):
                            makeRepeatTopTaskChanged(dataEvent, actId, timestamp, rankId, rankingUpdateResult, phaseTaskResult);
                    sendMessage(event, "TaskProgressChanged-" + inx + ":" + seq);
                }
            } catch (Throwable t) {
                log.error("notifyPhaseTaskChange exception@inx:{}, seq:{}, rankingUpdateResult:{}, err:{} {}", inx, seq,
                        rankingUpdateResult, t.getMessage(), clock.tag(), t);
            }
        }
    }

    private TaskProgressChanged makeTaskProgressChanged(RankDataEvent dataEvent, long actId, String timestamp, Long rankId,
                                                        RankingUpdateResult rankingUpdateResult, PhaseTaskResult phaseTaskResult) {
        TaskProgressChanged event = new TaskProgressChanged();
        event.setBusiId(dataEvent.getBusiId());
        event.setActId(rankingUpdateResult.getActId());
        event.setRankId(rankingUpdateResult.getRankId());
        event.setPhaseId(rankingUpdateResult.getPhaseId());
        event.setTimeKey(cacheService.getRankingConfig(actId, rankId).getTimeKey());
        event.setCurrRound(phaseTaskResult.getCurrRound());
        event.setCurrTaskIndex(phaseTaskResult.getCurrTaskIndex());
        event.setRoundComplete(phaseTaskResult.getRoundComplete());
        event.setStartTaskIndex(phaseTaskResult.getStartTaskIndex());
        event.setItemLevelPassMap(phaseTaskResult.getItemLevelPassMap());
        event.setItemCurrNumMap(phaseTaskResult.getItemCurrNumMap());
        event.setTimestamp(timestamp);
        event.setOccurTime(DateUtil.format(new Date(dataEvent.getTimestamp())));
        event.setActors(dataEvent.getActors());
        event.setMember(rankingUpdateResult.getMember());
        event.setRankScore(rankingUpdateResult.getRankScore());
        event.setPhaseScore(rankingUpdateResult.getPhaseScore());
        return event;
    }

    private RepeatTopTaskChanged makeRepeatTopTaskChanged(RankDataEvent dataEvent, long actId, String timestamp, Long rankId,
                                                          RankingUpdateResult rankingUpdateResult, PhaseTaskResult phaseTaskResult) {
        RepeatTopTaskChanged event = new RepeatTopTaskChanged();
        event.setBusiId(dataEvent.getBusiId());
        event.setActId(rankingUpdateResult.getActId());
        event.setRankId(rankingUpdateResult.getRankId());
        event.setPhaseId(rankingUpdateResult.getPhaseId());
        event.setTimeKey(cacheService.getRankingConfig(actId, rankId).getTimeKey());
        event.setCurrRound(phaseTaskResult.getCurrRound());
        event.setCurrTaskIndex(phaseTaskResult.getCurrTaskIndex());
        event.setRoundComplete(phaseTaskResult.getRoundComplete());
        event.setStartTaskIndex(phaseTaskResult.getStartTaskIndex());
        event.setItemLevelPassMap(phaseTaskResult.getItemLevelPassMap());
        event.setItemCurrNumMap(phaseTaskResult.getItemCurrNumMap());
        event.setTimestamp(timestamp);
        event.setOccurTime(DateUtil.format(new Date(dataEvent.getTimestamp())));
        event.setActors(dataEvent.getActors());
        event.setMember(rankingUpdateResult.getMember());
        return event;
    }

    /**
     * 保存发送标记 - 使用累积次数的方式，调用者可以判断是否等于 1 来判断是否首次并做必要逻辑
     * @param actId
     * @param hashKey
     * @return
     */
    private long saveSendMsgFlag(long actId, String hashKey, String hint) {
        long group = redisGroupService.getGroupCode(actId, RedisGroupService.RANK_ID_NON);
        String key = KeyService.makeKeyNoRankIdPrefix(actId) + TIME_EVENT_NOTIFY;

        // 保存首次发送记录备查
        Const.EXECUTOR_GENERAL.execute(new Runnable() {
            @Override
            public void run() {
                String bakKey = key + ".bak";
                try {
                    rankingRedisDao.hsetnx(group, bakKey, hashKey, hint);
                } catch (Throwable t) {
                    log.error("saveSendMsgFlag exception@bakKey:{}, hashKey:{}, hint:{}, err:{}", bakKey, hashKey, hint, t.getMessage());
                }
            }
        });

        return rankingRedisDao.hIncrByKey(group, key, hashKey, 1);
    }

    /**
     * 发送消息， 返回 true 表示发送到MQ成功了（MQ保证），返回false不一定失败了（叫未知更准确）
     */
    private boolean sendMessage(BaseEvent event, String tag) {
        // 使用 OtelExtendService 设置上下文并执行发送逻辑
        final boolean[] result = {false};

        otelExtendService.setOtelContextForEvent(event, () -> {
            Clock clock = new Clock();
            String message = event.getUri() + "|" + JSON.toJSONString(event);
            try {
                hdztWxKafkaTemplate.send(rankingEventsTopic, message).get(10, TimeUnit.SECONDS);
                log.info("sendMessage wx done@[{}] message:{} {}", tag, message, clock.tag());
                result[0] = true;
            } catch (Exception eWx) {
                log.error("sendMessage wx exception@seq:{}, err:{} {}", event.getSeq(), eWx.getMessage(), clock.tag(), eWx);
                try {
                    hdztSzKafkaTemplate.send(rankingEventsTopic, message).get(10, TimeUnit.SECONDS);
                    log.info("sendMessage sz done@[{}] message:{} {}", tag, message, clock.tag());
                    result[0] = true;
                } catch (Exception eSz) {
                    log.error("sendMessage sz exception@tag:{}, message:{}, err:{} {}", tag, message, eSz.getMessage(), clock.tag(), eSz);
                    saveToEventNotifyFailQueue(event, tag);
                    result[0] = false;
                }
            }
        });

        return result[0];
    }

    /**
     * 重试发送事件通知
     */
    public int redoEventNotify() {
        // 若空，睡眠 sleepMills 返回
        long sleepMills = 5000;
        if (eventNotifyFailQueue.isEmpty()) {
            SysEvHelper.waiting(sleepMills);
            return 0;
        }

        int inx = 0;
        Map<String, Object> map = null;
        while ((map = eventNotifyFailQueue.poll()) != null) {
            String tag = (String) (map.get("tag"));
            BaseEvent event = (BaseEvent) (map.get("event"));

            // 发送失败，说明MQ可能异常，睡眠等待一会后结束循环
            if (!sendMessage(event, tag)) {
                log.warn("redoEventNotify fail@inx:{}, tag:{}, event:{}", inx + 1, tag, event);
                SysEvHelper.waiting(sleepMills);
                break;
            }

            log.info("redoEventNotify one done@inx:{}, tag:{}, event:{}", ++inx, tag, event);
        }

        return inx;
    }

    /**
     * 本地进程缓存发送失败的消息
     */
    private void saveToEventNotifyFailQueue(BaseEvent event, String tag) {
        if (needRetryForEvent(event)) {
            Map<String, Object> map = ImmutableMap.of("event", event, "tag", tag);
            String content = JSON.toJSONString(map);
            try {
                // 超过 EventNotifyTimer.NOTIFY_SCAN_SPAN_SECONDS 秒的消息不再重试(绝大部分情况下是合理的）
                long eSeconds = DateUtil.getDate(event.getTimestamp()).getTime() / 1000;
                long diffSeconds = System.currentTimeMillis() / 1000 - eSeconds;
                if (diffSeconds > Const.NOTIFY_SCAN_SPAN_SECONDS) {
                    log.error("saveToEventNotifyFailQueue fail@delay too long:{}, content:{}, size:{}", diffSeconds, content, eventNotifyFailQueue.size());
                    return;
                }
                boolean flag = eventNotifyFailQueue.add(map);
                log.info("saveToEventNotifyFailQueue ok@content:{}, flag:{}, size:{}", content, flag, eventNotifyFailQueue.size());
            } catch (Throwable t) {
                log.error("saveToEventNotifyFailQueue exception@content:{}, size:{}", content, eventNotifyFailQueue.size());
            }
        }
    }

    /**
     * 判断事件是否需要重试发送
     * @param event
     * @return
     */
    private boolean needRetryForEvent(BaseEvent event) {
        /*
         1.榜单阶段任务进度变化 + 榜单分值变化 两种情况做失败缓存重试
         2.其它消息类型目前有自己的重试机制
         3.可根据业务需要，增加新的重试事件uri
         */
        //long uri = event.getUri();
        //return uri == BaseEvent.TASK_PROGRESS_CHANGED || uri == BaseEvent.RANKING_SCORE_CHANGED;
        return StringUtil.isBlank(event.getEkey());
    }

    /**
     * 处理任务变化事件
     * <AUTHOR>
     * @date 2020年7月30日 下午7:57:46 
     */
    public void processTaskProgressChanged(TaskProgressChanged result) {
        long actId = result.getActId();
        String seq = "TPC-" + actId + ":" + result.getSeq();
        // 随机散开防止集中过期
        int expireSeconds = 600 + Const.RD.nextInt(600);
        if (rankingRedisDao.setNX(HdztConst.REDIS_BASE_GROUP, seq, DateUtil.format(hdztActivityService.getCurrSysTime(actId)), expireSeconds)) {
            long rankId = result.getRankId();
            long phaseId = result.getPhaseId();
            processTaskAward(result, actId, rankId, phaseId);
        } else {
            log.error("processTaskProgressChanged fail@repeated event:{}", result);
        }
    }

    /**
     * 完成任务发奖
     * <AUTHOR>
     * @date 2020年7月30日 下午10:25:34 
     */
    private void processTaskAward(TaskProgressChanged result, long actId, long rankId, long phaseId) {
        String seq = result.getSeq();
        String member = result.getMember();
        final long receiver = Convert.toLong(member, -1);
        if (receiver == -1) {
            log.warn("processTaskAward fail@member:{} is not number! seq:{}", member, seq);
            return;
        }

        // 使用 task_level 排序了，但并不保证 task_level从1开始连续递增
        Map<Long, RankingTask> rankingTaskMap = cacheService.getRankingTaskMap(actId, rankId, phaseId);

        // 将 task_level 值转成从 1 开始的连续数值
        long counter = 1;
        TreeMap<Long, Map<Long, RankingTaskAwardConfig>> awardMap = Maps.newTreeMap();
        for (Long taskId : rankingTaskMap.keySet()) {
            Map<Long, RankingTaskAwardConfig> rankingTaskAwardMap = cacheService.getRankingTaskAwardConfigMap(actId, rankId, phaseId, taskId);
            awardMap.put(counter++, rankingTaskAwardMap);
        }

        // 奖励在  packageId 下统计总数, 第一层key:poolId, 第二层key：packageId 
        final Map<Long, AwardCounterMap> awardCounterMap = Maps.newHashMap();

        /*
         * 发奖励的算法为：
         * 1.先计算跨在最后站两边的零头过站奖励，
         * 2.然后遍历关下每个站奖励 ，对奖励数值 x （通关的总轮数-1），进行发放累计
         */
        long startTaskIndex = result.getStartTaskIndex();
        long currTaskIndex = result.getCurrTaskIndex();
        // 通关发法（过了关下的所有站）
        if (result.getRoundComplete() > 0) {
            // 从 start 到最后一个 任务级别 的奖励
            NavigableMap<Long, Map<Long, RankingTaskAwardConfig>> tailMap = awardMap.tailMap(startTaskIndex, false);

            // 从第一个 station到curStation的奖励
            NavigableMap<Long, Map<Long, RankingTaskAwardConfig>> headMap = awardMap.headMap(currTaskIndex, true);

            // 统计尾部
            calc(awardCounterMap, tailMap, 1);

            // 统计头部
            calc(awardCounterMap, headMap, 1);

            // 统计完全通关奖励
            long totalRound = result.getRoundComplete() - 1;
            if (totalRound > 0) {
                calc(awardCounterMap, awardMap, totalRound);
            }
        } else { // 非通关发法（没有过完关卡下的所有站）
            // 若小于了，则一定发生了通关！！！
            if (currTaskIndex > startTaskIndex) {
                NavigableMap<Long, Map<Long, RankingTaskAwardConfig>> subMap = awardMap.subMap(startTaskIndex, false, currTaskIndex, true);
                calc(awardCounterMap, subMap, 1);
            }
        }

        awardCounterMap.remove(0L);
        if (CollectionUtils.isEmpty(awardCounterMap)) {
            log.info("processTaskAward done@no award item to issue, seq:{}, member:{}", seq, member);
            return;
        }

        /************ 异步发放奖励 *************/
        Const.EXECUTOR_GENERAL.execute(new Runnable() {
            @Override
            public void run() {
                RankingTaskAwardIssue rtai = new RankingTaskAwardIssue();
                try {
                    long actId = Convert.toLong(result.getActId());
                    Date date = DateUtil.getDate(result.getTimestamp());
                    String tag = String.format("[TaskProgressChanged] level:%s -> %s, round: +%s / %s",
                            result.getStartTaskIndex(), result.getCurrTaskIndex(), result.getRoundComplete(), result.getCurrRound()) + ", " + seq;

                    // 先入库
                    long issueId = Const.genId(Const.IG, Const.RANKING_ISSUE_ID, date);
                    rtai.setIssueId(issueId);
                    rtai.setActId(actId);
                    rtai.setRankId(rankId);
                    rtai.setPhaseId(phaseId);
                    rtai.setReceiver(receiver);
                    rtai.setPackageList(JSON.toJSONString(awardCounterMap));
                    rtai.setStatus(0L); // 还未发放
                    rtai.setTag(tag);
                    rtai.setRemark("过任务发奖励,待发放中。。。");
                    rtai.setExtjson("");
                    rtai.setCtime(date);
                    rtai.setUtime(date);
                    rankingMysqlDao.insert(RankingTaskAwardIssue.class, rtai);

                    // 再发放
                    taskAwardIssueService.issue(rtai);
                    log.info("processTaskAward issue ok@rtai:{}", rtai);
                } catch (Exception e) {
                    log.error("processTaskAward issue fail@rtai:{}, err:{}", rtai, e, e);
                }
            }
        });
    }

    /**
     * 统计函数
     * <AUTHOR>
     * @date 2020年7月30日 下午10:21:52 
     */
    private void calc(Map<Long, AwardCounterMap> awardCounterMap, NavigableMap<Long, Map<Long, RankingTaskAwardConfig>> navigableMap, long rate) {
        if (MapUtils.isEmpty(navigableMap)) {
            return;
        }

        for (Map<Long, RankingTaskAwardConfig> awards : navigableMap.values()) {
            for (Long packageId : awards.keySet()) {
                RankingTaskAwardConfig awrad = awards.get(packageId);
                Long poolId = awrad.getPoolId();
                AwardCounterMap counterMap = awardCounterMap.get(poolId);
                if (counterMap == null) {
                    counterMap = new AwardCounterMap();
                    awardCounterMap.put(poolId, counterMap);
                }
                counterMap.put(packageId, awrad.getPackageNum() * rate);
            }
        }
    }

    /**
     * 查找需要报告 开始/结束 的阶段
     * <AUTHOR>
     * @date 2020年7月29日 下午4:59:22 
     */
    public RankingPhase getRankPhaseForNotify(long actId, Date now, List<RankingPhase> phases) {
        if (CollectionUtils.isEmpty(phases)) {
            return null;
        }

        // 1. 若所有的阶段都还没开始，返回 null
        long nowSeconds = now.getTime() / 1000;
        long beginSeconds = phases.get(0).getBeginTime().getTime() / 1000;
        if (nowSeconds < beginSeconds) {
            return null;
        }

        // 2. 遍历找到第一个时间区间符合的阶段信息对象（配置人员必须自己保证不重叠！）
        int size = phases.size();
        for (int i = 0; i < size; i++) {
            RankingPhase harPhase = phases.get(i);
            long timeBeginSeconds = harPhase.getBeginTime().getTime() / 1000;
            long timeEndSeconds = harPhase.getEndTime().getTime() / 1000;

            // a. 若处在第 i 个时段的起止时间内，返回 i 阶段
            if (nowSeconds >= timeBeginSeconds && nowSeconds <= timeEndSeconds) {
                return harPhase;
            }

            // b. 若i不是最后一个阶段，并且 i ~ i+1 之间有时间空隙，则也 返回 i 阶段
            if (i != size - 1) {
                long nextBeginSeconds = phases.get(i + 1).getBeginTime().getTime() / 1000;
                if (nowSeconds > timeEndSeconds && nowSeconds < nextBeginSeconds) {
                    return harPhase;
                }
            }
        }

        // 3. 则表明已结束了所有阶段，返回最后一个阶段
        return phases.get(size - 1);
    }

    /**
     * 通知榜单分值变化
     */
    public void notifyRankingScoreChange(RankDataEvent dataEvent, String seq, Map<Long, RankingUpdateResult> result) {
        int inx = 0;
        long actId = dataEvent.getActId();
        String timestamp = DateUtil.format(hdztActivityService.getCurrSysTime(actId));
        for (Long rankId : result.keySet()) {
            inx++;
            Clock clock = new Clock();
            RankingUpdateResult rankingUpdateResult = result.get(rankId);
            try {
                if(cacheService.findRankingConfigExt(actId, rankId).getSend_ranking_score_changed_event() == 1) {
                    RankingScoreChanged event = new RankingScoreChanged();
                    event.setBusiId(dataEvent.getBusiId());
                    event.setActId(rankingUpdateResult.getActId());
                    event.setRankId(rankingUpdateResult.getRankId());
                    event.setPhaseId(rankingUpdateResult.getPhaseId());
                    event.setTimeKey(cacheService.getRankingConfig(actId, rankId).getTimeKey());
                    event.setTimestamp(timestamp);
                    event.setOccurTime(DateUtil.format(new Date(dataEvent.getTimestamp())));
                    event.setActors(dataEvent.getActors());
                    event.setMember(rankingUpdateResult.getMember());

                    event.setItemId(rankingUpdateResult.getItemId());
                    event.setItemCount(rankingUpdateResult.getItemCount());
                    event.setItemScore(rankingUpdateResult.getItemScore());

                    event.setRankScore(rankingUpdateResult.getRankScore());
                    event.setPhaseScore(rankingUpdateResult.getPhaseScore());

                    //透传现场给下游用
                    event.setRoleCounts(dataEvent.getRoleCounts());
                    event.setRoleScores(dataEvent.getRoleScores());
                    event.setRankCounts(dataEvent.getRankCounts());
                    event.setRankScores(dataEvent.getRankScores());

                    sendMessage(event, "RankingScoreChanged-" + inx + ":" + seq);
                }
            } catch (Throwable t) {
                log.error("notifyRankingScoreChange exception@inx:{}, seq:{}, rankingUpdateResult:{}, err:{} {}", inx, seq,
                        rankingUpdateResult, t.getMessage(), clock.tag(), t);
            }
        }
    }


    public void notifyMemberScoreChange(RankDataEvent dataEvent, String seq, Map<Long, RankingUpdateResult> result) {
        if (CollectionUtils.isEmpty(result)) {
            return;
        }

        Clock clock = new Clock();
        long actId = dataEvent.getActId();
        try {
            Map<Long, Set<String>> members = Maps.newHashMap();
            for (Long rankId : result.keySet()) {
                String roles = cacheService.getRankingMember(actId, rankId).getRoles();
                String member = result.get(rankId).getMember();
                List<KeyValuePair> pairs = getElementAndRoleTypeList(roles, member);
                if (!CollectionUtils.isEmpty(pairs)) {
                    for (KeyValuePair pair : pairs) {
                        Long roleType = pair.getValue();
                        Set<String> set = members.get(roleType);
                        if (set == null) {
                            set = Sets.newHashSet();
                            members.put(roleType, set);
                        }
                        set.add(pair.getKey());
                    }
                }
            }

            if (CollectionUtils.isEmpty(members)) {
                return;
            }

            if (cacheService.findActivityConfigExt(actId).getSend_member_score_changed_event() == 1) {
                MemberScoreChanged event = new MemberScoreChanged();
                event.setBusiId(dataEvent.getBusiId());
                event.setActId(dataEvent.getActId());
                event.setTimestamp(DateUtil.format(hdztActivityService.getCurrSysTime(actId)));
                event.setOccurTime(DateUtil.format(new Date(dataEvent.getTimestamp())));
                event.setMembers(members);
                event.setActors(dataEvent.getActors());
                sendMessage(event, "notifyMemberScoreChange:" + seq);
            }

        } catch (Throwable t) {
            log.error("notifyMemberScoreChange exception@actId:{}, seq:{}, result size:{}, err:{} {}", actId, seq, result.size(), t.getMessage(), clock.tag(), t);
        }
    }

    /**
     * 获取 角色类型/成员元素值 对
     * @author: 郭立平[<EMAIL>]
     * @date: 2021/3/2 16:34
     **/
    public List<KeyValuePair> getElementAndRoleTypeList(String roles, String member) {
        if (StringUtil.isBlank(roles)) {
            return null;
        }
        String[] roleIdGroups = roles.split("\\|");
        if (roleIdGroups == null || roleIdGroups.length == 0) {
            return null;
        }

        String first = makeRoleTypeGroup(roleIdGroups[0]);
        if (!hasSameRoleType(first, roleIdGroups)) {
            return null;
        }

        List<KeyValuePair> pairs = Lists.newArrayList();
        String[] roleTypes = first.split("&");
        String[] elements = member.split("|");
        for (int i = 0; i < roleTypes.length; i++) {
            long roleType = Long.parseLong(roleTypes[i]);
            String element = elements[i];
            pairs.add(new KeyValuePair(element, roleType));
        }
        return pairs;
    }

    /**
     * 提取出 roleIdGroup 上的 roleType
     * @author: 郭立平[<EMAIL>]
     * @date: 2021/3/2 15:56
     **/
    private String makeRoleTypeGroup(String roleIdGroup) {
        String[] roleInfos = roleIdGroup.split("&");
        for (int i = 0; i < roleInfos.length; i++) {
            long roleId = Long.parseLong(roleInfos[i]);
            roleInfos[i] = cacheService.getHdztActor(roleId).getType().toString();
        }
        return StringUtils.join(roleInfos, "&");
    }

    /**
     * 判断组合角色的所有角色类型是否相同
     * @author: 郭立平[<EMAIL>]
     * @date: 2021/3/2 16:06
     **/
    private boolean hasSameRoleType(String target, String[] roleIdGroups) {
        if (roleIdGroups == null || roleIdGroups.length == 0) {
            return false;
        }
        for (String roleIdGroup : roleIdGroups) {
            if (!target.equals(makeRoleTypeGroup(roleIdGroup))) {
                return false;
            }
        }
        return true;
    }

    /**
     * 异步发如流通知， 绝对不可抛异常，以免影响其它处理
     */
    public void sendPkSettleTimeEndMessage(PkSettleTimeEnd event) {
        try {
            long actId = event.getActId();
            HdztActivity hdztActivity = cacheService.getHdztActivity(actId);
            StringBuffer buf = new StringBuffer("### <font color=\"green\">【榜单引擎PK结算成功】</font>\n");
            buf.append("#### [" + actId + "]" + hdztActivity.getActName() + "\n");
            buf.append("day：" + event.getDay() + "\n");
            buf.append("promotKey：" + event.getPromotKey() + "\n");
            buf.append("pkSettleKey：" + event.getPkSettleKey() + "\n");
            buf.append("endTime：" + event.getEndTime() + "\n");
            buf.append("ekey：" + event.getEkey() + "\n");
            buf.append("seq:" + event.getSeq() + "\n");
            buf.append("index：" + event.getIndex() + "\n");
            buf.append("ts：" + event.getTimestamp() + "\n");
            baiduInfoFlowRobotService.asyncSendNotifyConfigKey(ParamName.IMGroup.IMG_IT_RUNNING_STATUS, buf.toString(), null);
        }catch(Throwable e) {
            log.error("sendPromotTimeEndMessage exception@event:{}, err:{}", event, e.getMessage(), e);
        }
    }

    /**
     * 异步发如流通知， 绝对不可抛异常，以免影响其它处理
     */
    public void sendPromotTimeEndMessage(PromotTimeEnd event) {
        try {
            long actId = event.getActId();
            HdztActivity hdztActivity = cacheService.getHdztActivity(actId);
            StringBuffer buf = new StringBuffer("### <font color=\"green\">【榜单引擎晋级结算成功】</font>\n");
            buf.append("#### [" + actId + "]" + hdztActivity.getActName() + "\n");
            buf.append("promotKey：" + event.getPromotKey() + "\n");
            buf.append("endTime：" + event.getEndTime() + "\n");
            buf.append("ekey：" + event.getEkey() + "\n");
            buf.append("seq:" + event.getSeq() + "\n");
            buf.append("index：" + event.getIndex() + "\n");
            buf.append("ts：" + event.getTimestamp() + "\n");
            baiduInfoFlowRobotService.asyncSendNotifyConfigKey(ParamName.IMGroup.IMG_IT_RUNNING_STATUS, buf.toString(), null);
        }catch(Throwable e) {
            log.error("sendPromotTimeEndMessage exception@event:{}, err:{}", event, e.getMessage(), e);
        }
    }
}
