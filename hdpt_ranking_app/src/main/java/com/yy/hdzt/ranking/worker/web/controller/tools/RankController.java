
package com.yy.hdzt.ranking.worker.web.controller.tools;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yy.hdzt.common.bean.Response;
import com.yy.hdzt.common.exception.SuperException;
import com.yy.hdzt.common.support.SysEvHelper;
import com.yy.hdzt.common.thrift.hdzt.ranking.MemberGroup;
import com.yy.hdzt.common.thrift.hdzt.ranking.PkInfo;
import com.google.common.collect.ImmutableMap;
import com.yy.hdzt.common.utils.Convert;
import com.yy.hdzt.common.utils.DateUtil;
import com.yy.hdzt.common.utils.RequestUtil;
import com.yy.hdzt.common.utils.StringUtil;
import com.yy.hdzt.ranking.dao.redis.RankingRedisDao;
import com.yy.hdzt.ranking.event.RankDataEvent;
import com.yy.hdzt.ranking.exception.BadRequestException;
import com.yy.hdzt.ranking.service.*;
import com.yy.hdzt.ranking.service.datatransfer.RedisDataTransferService;
import com.yy.hdzt.ranking.service.impl.RankServiceImpl;
import org.apache.commons.lang3.ArrayUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.SendResult;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;


@Validated
@RestController
@RequestMapping(value = "/hdzt_20200623_is_good")
public class RankController extends BaseController {
    private static final Logger log = LoggerFactory.getLogger(RankController.class);

    @Autowired
    private RankServiceImpl rankServiceImpl;

    @Autowired
    private RankingRedisDao rankRedisDao;

    @Autowired
    @Qualifier("hdztWxKafkaTemplate")
    private KafkaTemplate<String, String> hdztWxKafkaTemplate;

    @Autowired
    @Qualifier("hdztSzKafkaTemplate")
    private KafkaTemplate<String, String> hdztSzKafkaTemplate;

    @Autowired
    private CacheService cacheService;

    @Value("${kafka.hdzt.ranking.updating.topic}")
    private String rankingUpdatingTopic;

    @Autowired
    private RedisDataTransferService redisDataTransferService;

    @Autowired
    private PhasePkgroupService phasePkgroupService;

    @Autowired
    private RankingPhaseQualificationService phaseQualificationService;

    @Autowired
    private DynamicPkService dynamicPkService;

    @Autowired
    private RedisGroupService redisGroupService;

    /**
     * 获取 pk信息 -测试接口
     */
    @RequestMapping(value = "/queryPkInfo")
    @ResponseBody
    public Response queryPkInfo(HttpServletRequest request,
                                @NotNull @Min(1) @RequestParam(name = "actId") Long actId,
                                @NotNull @Min(1) @RequestParam(name = "srcRankId") Long srcRankId,
                                Long phaseId, Integer returnData, String dateStr, String queryDateStr) throws Exception {
        checkEnvForGrey(request, actId);

        phaseId = Convert.toLong(phaseId);
        dateStr = Convert.toString(dateStr);
        returnData = Convert.toInt(returnData);
        queryDateStr = Convert.toString(queryDateStr);

        PkInfo pkInfo = phasePkgroupService.getPkInfo(actId, srcRankId, phaseId, dateStr, returnData, queryDateStr);
        return Response.success(pkInfo);
    }


    /**
     * 获取 分组信息  -测试接口
     */
    @RequestMapping(value = "/queryQualificationGroup")
    @ResponseBody
    public Object queryQualificationGroup(HttpServletRequest request,
                                          @NotNull @Min(1) @RequestParam(name = "actId") Long actId,
                                          @NotNull @Min(1) @RequestParam(name = "rankId") Long rankId,
                                          Long phaseId, Integer returnData, String queryDateStr) throws Exception {
        checkEnvForGrey(request, actId);

        phaseId = Convert.toLong(phaseId);
        returnData = Convert.toInt(returnData);
        queryDateStr = Convert.toString(queryDateStr);

        MemberGroup memberGroup = phaseQualificationService.queryQualificationGroup(actId, rankId, phaseId, returnData, queryDateStr);
        return Response.success(memberGroup);
    }


    @RequestMapping(value = "/intPkMembers")
    @ResponseBody
    public Object intDynamicTreePkMembers(HttpServletRequest request, HttpServletResponse resp,
                                          @NotNull @Min(1) @RequestParam(name = "actId") Long actId,
                                          @NotNull @Min(1) @RequestParam(name = "rankId") Long rankId,
                                          @NotNull @Min(1) @RequestParam(name = "phaseId") long phaseId, String seq,
                                          @NotNull @RequestParam(name = "pkMembers") String pkMembers) throws Exception {

        long opUid = getLoginYYUid(request, resp);
        if (!SysEvHelper.isLocal()) {
            String ip = RequestUtil.getRealIp(request);
            if (!isOfficeIp(ip)) {
                throw new BadRequestException("只允许办公网IP发起请求， your ip:" + ip);
            }
            if (opUid == 0) {
                throw new BadRequestException("请登录");
            }
        }

        String admin = cacheService.findActivityConfigExt(actId).getDynamic_pk_admin();
        Assert.isTrue(ArrayUtils.contains(admin.split(","), String.valueOf(opUid)), "权限不足，无法设置");

        return dynamicPkService.setDynamicPkMembers(actId, rankId, phaseId, pkMembers, opUid, seq);

    }

    @RequestMapping(value = "/getPkMembersPreview")
    @ResponseBody
    public Object getPkMembersPreview(HttpServletRequest request, HttpServletResponse resp,
                                      @NotNull @Min(1) @RequestParam(name = "actId") Long actId,
                                      @NotNull @Min(1) @RequestParam(name = "rankId") Long rankId,
                                      @NotNull @Min(1) @RequestParam(name = "phaseId") long phaseId) throws Exception {

        long opUid = getLoginYYUid(request, resp);
        if (!SysEvHelper.isLocal()) {
            String ip = RequestUtil.getRealIp(request);
            if (!isOfficeIp(ip)) {
                throw new BadRequestException("只允许办公网IP发起请求， your ip:" + ip);
            }
        }
        return dynamicPkService.getPkMembersPreview(actId, rankId, phaseId, opUid);
    }


    @RequestMapping(value = "/updateRankingByMQ")
    @ResponseBody
    public Object updateRankingByMQ(HttpServletRequest request,
                                    @NotNull @Min(1) @RequestParam(name = "actId") Long actId,
                                    @NotNull @RequestParam(name = "actors") String text1,
                                    @RequestParam(name = "seq") String seq) throws Exception {

        text1 = text1.trim();
        seq = Convert.toString(seq, UUID.randomUUID().toString());
        checkEnvForGrey(request, actId);


        @SuppressWarnings("unchecked")
        Map<Integer, String> map1 = (Map<Integer, String>) JSON.parseObject(text1, Map.class);
        Map<Long, String> actors = Maps.newHashMap();
        if (!CollectionUtils.isEmpty(map1)) {
            for (Integer key : map1.keySet()) {
                actors.put(new Long(key), map1.get(key));
            }
        }
        String itemId = Convert.toString(request.getParameter("itemId"), "BBT_001");
        long count = Convert.toLong(request.getParameter("count"), 10);
        long score = count * 10;
        String ip = RequestUtil.getRealIp(request);

        String time = Convert.toString(request.getParameter("time"), DateUtil.today("yyyyMMddHHmmss"));
        long timestamp = DateUtil.getDate(time, "yyyyMMddHHmmss").getTime();

        RankDataEvent r = new RankDataEvent();
        r.setBusiId(400);
        r.setActId(actId);
        r.setSeq(seq);
        r.setActors(actors);
        r.setItemId(itemId);
        r.setCount(count);
        r.setScore(score);
        r.setIp(ip);
        r.setTimestamp(timestamp);

        String message = "1|" + JSON.toJSONString(r);
        SendResult<String, String> rwx = hdztWxKafkaTemplate.send(rankingUpdatingTopic, "A, " + message).get();
        SendResult<String, String> rsz = hdztSzKafkaTemplate.send(rankingUpdatingTopic, "B, " + message).get();
        log.info("updateRankingByMQ ok@put routeKey:{}, message:{}, rwx:{}, rsz:{}", rankingUpdatingTopic, message, rwx, rsz);
        return "ok";

    }

    /**
     * 更新榜单
     */
    @RequestMapping(value = "/updateRanking")
    @ResponseBody
    public Object updateRanking(HttpServletRequest request,
                                @NotNull @RequestParam(name = "content") String content) throws Exception {
        content = content.trim();
        RankDataEvent dataEvent = JSON.parseObject(content, RankDataEvent.class);
        long actId = dataEvent.getActId();

        checkEnvForGrey(request, actId);
        String seq = StringUtil.trim(dataEvent.getSeq());
        if (seq.isEmpty()) {
            dataEvent.setSeq("TEST-" + UUID.randomUUID().toString());
        }
        long timestamp = dataEvent.getTimestamp();
        if (timestamp <= 0) {
            dataEvent.setTimestamp(hdztActivityService.getCurrSysTime(actId).getTime());
        }

        // 做相关转换
        if (hdztActivityService.adjust(dataEvent)) {
            rankServiceImpl.updateRanking(dataEvent);
        }
        return new Object[]{"updateRanking", dataEvent, "ok"};
    }

    /**
     * 获取 zset key 下指定范围的排名数据
     */
    @RequestMapping(value = "/zrevRange")
    @ResponseBody
    public Response zrevRange(HttpServletRequest request,
                              @NotNull @Min(1) @RequestParam(name = "actId") Long actId,
                              @NotNull @RequestParam(name = "key") String key,
                              Long start, Long end, long rankId) throws Exception {

        checkEnvForGrey(request, actId);

        start = Convert.toLong(start, 0);
        end = Convert.toLong(end, -1);

        long group = redisGroupService.getGroupCode(actId, rankId);
        Set<ZSetOperations.TypedTuple<String>> set = rankRedisDao.zrevRange(group, key, start, end);
        List<String> result = Lists.newArrayList();
        int inx = 0;
        for (ZSetOperations.TypedTuple<String> tuple : set) {
            inx++;
            String member = tuple.getValue();
            long score = tuple.getScore().longValue();
            result.add("(" + inx + ") " + member + " -> " + score);
        }
        return Response.success(ImmutableMap.of("method", "zrevRange", "key", key + " " + start + " " + end));
    }

    /**
     * 获取 zset key 下指定 member 的分值 和 排名
     */
    @RequestMapping(value = "/zScoreAndRank")
    @ResponseBody
    public Response zScoreAndRank(HttpServletRequest request,
                                  @NotNull @Min(1) @RequestParam(name = "actId") Long actId,
                                  @NotNull @RequestParam(name = "key") String key,
                                  @NotNull @RequestParam(name = "member") String member
                                    ,long rankId) throws Exception {

        checkEnvForGrey(request, actId);
        long group = redisGroupService.getGroupCode(actId, rankId);
        List<Object> result = rankRedisDao.zScoreAndRank(group, key, member);

        return Response.success(ImmutableMap.of("method", "zScoreAndRank", "key", key + " " + member, "result", result));
    }

    /**
     * 增加 zset key 下指定 member 的分值
     */
    @RequestMapping(value = "/zIncr")
    @ResponseBody
    public Response zIncr(HttpServletRequest request,
                          @NotNull @Min(1) @RequestParam(name = "actId") Long actId,
                          @NotNull @RequestParam(name = "key") String key,
                          @NotNull @RequestParam(name = "member") String member,
                          Long score, long rankId) throws Exception {

        score = Convert.toLong(score);
        checkEnvForGrey(request, actId);
        long group = redisGroupService.getGroupCode(actId, rankId);
        Double result = rankRedisDao.zIncr(group, key, member, score);
        return Response.success(ImmutableMap.of("method", "zIncr", "key", key + " " + member + " " + score, "result", result.longValue()));
    }

    /**
     * 删除 zset key 下指定 member
     */
    @RequestMapping(value = "/zDel")
    @ResponseBody
    public Response zDel(HttpServletRequest request,
                         @NotNull @Min(1) @RequestParam(name = "actId") Long actId,
                         @NotNull @RequestParam(name = "key") String key,
                         @NotNull @RequestParam(name = "member") String member,long rankId) throws Exception {

        checkEnvForGrey(request, actId);
        long group = redisGroupService.getGroupCode(actId, rankId);
        rankRedisDao.zDel(group, key, member);
        return Response.success(ImmutableMap.of("method", "zDel", "key", key + " " + member));

    }

    /**
     * 增加 set key 下指定 member
     */
    @RequestMapping(value = "/sadd")
    @ResponseBody
    public Response sAdd(HttpServletRequest request,
                         @NotNull @Min(1) @RequestParam(name = "actId") Long actId,
                         @NotNull @RequestParam(name = "key") String key,
                         @NotNull @RequestParam(name = "member") String member,long rankId) throws Exception {

        checkEnvForGrey(request, actId);
        long group = redisGroupService.getGroupCode(actId, rankId);
        Long result = rankRedisDao.sadd(group, key, member);
        return Response.success(ImmutableMap.of("method", "sAdd", "key", key + " " + member, "result", result.longValue()));
    }

    /**
     * 删除 set key 下指定 member
     */
    @RequestMapping(value = "/sDel")
    @ResponseBody
    public Response sDel(HttpServletRequest request, @NotNull @Min(1) @RequestParam(name = "actId") Long actId,
                         @NotNull @RequestParam(name = "key") String key,
                         @NotNull @RequestParam(name = "member") String member,long rankId) throws Exception {

        checkEnvForGrey(request, actId);
        long group = redisGroupService.getGroupCode(actId, rankId);
        Long result = rankRedisDao.srem(group, key, member);
        return Response.success(ImmutableMap.of("method", "sDel", "key", key + " " + member, "result", result));

    }

    /**
     * 获取 set key 下所有 member
     */
    @RequestMapping(value = "/sMembers")
    @ResponseBody
    public Response sMembers(HttpServletRequest request,
                             @NotNull @Min(1) @RequestParam(name = "actId") Long actId,
                             @NotNull @RequestParam(name = "key") String key, long rankId) throws Exception {


        checkEnvForGrey(request, actId);
        long group = redisGroupService.getGroupCode(actId, rankId);
        Set<String> result = rankRedisDao.smembers(group, key);
        return Response.success(ImmutableMap.of("method", "sMembers", "key", key, "result", result));

    }

    /**
     * 获取 hash key 所有 field 的内容
     */
    @RequestMapping(value = "/hGetAll")
    @ResponseBody
    public Response hGetAll(HttpServletRequest request
            , @NotNull @Min(1) @RequestParam(name = "actId") Long actId,
                            @NotNull @RequestParam(name = "key") String key, long rankId) throws Exception {

        checkEnvForGrey(request, actId);
        long group = redisGroupService.getGroupCode(actId, rankId);
        Map<Object, Object> result = rankRedisDao.hGetAll(group, key);

        return Response.success(ImmutableMap.of("method", "hGetAll", "key", key, "result", result));

    }

    /**
     * 获取 hash key 下指定 field 的内容
     */
    @RequestMapping(value = "/hgetKey")
    @ResponseBody
    public Response hgetKey(HttpServletRequest request,
                            @NotNull @Min(1) @RequestParam(name = "actId") Long actId,
                            @NotNull @RequestParam(name = "key") String key,
                            @NotNull @RequestParam(name = "field") String field,long rankId) throws Exception {


        checkEnvForGrey(request, actId);
        long group = redisGroupService.getGroupCode(actId, rankId);
        String result = rankRedisDao.hgetKey(group, key, field);

        return Response.success(ImmutableMap.of("method", "hgetKey", "key", key + " " + field, "result", result));
    }

    /**
     * 设置 hash key 下指定 field 的内容
     */
    @RequestMapping(value = "/hsetKey")
    @ResponseBody
    public Response hsetKey(HttpServletRequest request,
                            @NotNull @Min(1) @RequestParam(name = "actId") Long actId,
                            @NotNull @RequestParam(name = "key") String key,
                            @NotNull @RequestParam(name = "field") String field,
                            @NotNull @RequestParam(name = "value") String value,long rankId) throws Exception {


        checkEnvForGrey(request, actId);
        long group = redisGroupService.getGroupCode(actId, rankId);
        rankRedisDao.hsetKey(group, key, field, value, -1);
        return Response.success(ImmutableMap.of("method", "hsetKey", "key", key + " " + field, "value", value));

    }

    /**
     * 删除 hash key 中的指定 field
     */
    @RequestMapping(value = "/hdelKey")
    @ResponseBody
    public Response hdelKey(HttpServletRequest request,
                            @NotNull @Min(1) @RequestParam(name = "actId") Long actId,
                            @NotNull @RequestParam(name = "key") String key,
                            @NotNull @RequestParam(name = "field") String field,long rankId) throws Exception {

        checkEnvForGrey(request, actId);
        long group = redisGroupService.getGroupCode(actId, rankId);
        rankRedisDao.hdelKey(group, key, Lists.newArrayList(field));
        return Response.success(ImmutableMap.of("method", "hdelKey", "key", key + " " + field));
    }

    /**
     * 删除任意key
     */
    @RequestMapping(value = "/del")
    @ResponseBody
    public Response del(HttpServletRequest request,
                        @NotNull @Min(1) @RequestParam(name = "actId") Long actId,
                        @NotNull @RequestParam(name = "key") String key, long rankId) throws Exception {

        checkEnvForGrey(request, actId);
        long group = redisGroupService.getGroupCode(actId, rankId);
        rankRedisDao.del(group, key);
        return Response.success(ImmutableMap.of("method", "del", "key", key));

    }

    /**
     * 列出指定模式的key
     */
    @RequestMapping(value = "/keys")
    @ResponseBody
    public Response keys(HttpServletRequest request,
                         @NotNull @Min(1) @RequestParam(name = "actId") Long actId,
                         @NotNull @RequestParam(name = "pattern") String pattern, long rankId) throws Exception {

        checkEnvForGrey(request, actId);
        String str = pattern.replaceAll("\\*", "");
        int num = 3;

        if (str.length() < num) {
            return Response.fail(-1, "明确的字符数不能少于" + num + "个：" + pattern);
        }
        long group = redisGroupService.getGroupCode(actId, rankId);
        Set<String> keys = rankRedisDao.keys(group, pattern);
        return Response.success(ImmutableMap.of("method", "keys", "pattern:", pattern, "keys", keys));

    }

    /**
     * 获取虚拟时间
     */
    @RequestMapping(value = "/getVirtTime")
    @ResponseBody
    public Response getVirtTime(HttpServletRequest request,
                                @NotNull @Min(1) @RequestParam(name = "actId") Long actId) throws Exception {
        String ip = RequestUtil.getRealIp(request);
        if (SysEvHelper.isDeploy() && !isOfficeIp(ip)) {
            throw new BadRequestException("只允许办公网IP发起请求，你的ip:" + ip);
        }
        boolean bGrey = cacheService.findActivityConfigExt(actId).getActivity_grey_status() == 1;

        String time = DateUtil.format(hdztActivityService.getCurrSysTime(actId));
        return Response.success(ImmutableMap.of("method", "getVirtTime", "grey_flag:", bGrey, "time", time));
    }

    /**
     * 设置虚拟时间
     */
    @RequestMapping(value = "/setVirtTime")
    @ResponseBody
    public Response setVirtTime(HttpServletRequest request,
                                @NotNull @Min(1) @RequestParam(name = "actId") Long actId,
                                @NotNull @RequestParam(name = "timestamp") String timestamp) throws Exception {
        log.info("setVirtTime actId:{}, timestamp:{}", actId, timestamp);
        checkEnvForGrey(request, actId);
        long group = redisGroupService.getGroupCode(actId, RedisGroupService.RANK_ID_NON);
        rankRedisDao.set(group, String.format(HdztActivityService.VIRT_TIME_KEY, actId), timestamp);
        return Response.success(ImmutableMap.of("method", "setVirtTime", "time", timestamp));
    }


    @RequestMapping("/copyHdzt2Pika")
    public Response copyHdzt2Pika(Long actId, Long uid, String action, String seq, Long time, String sign) {
        log.info("copyHdzk2Pika,seq:{},uid:{}", seq, uid);
        if (!checkManagerSign(time, action, actId, sign)) {
            log.warn("copyHdzt2Pika wrong time:{}, action:{},actId:{},sign:{}", time, action, actId, sign);
            return Response.fail(-1, "para error");
        }
        try {
            redisDataTransferService.addCopyRedisTask(actId, uid);
        } catch (Exception e) {
            return Response.fail(SuperException.E_FAIL, e.getMessage());
        }

        return Response.ok("发起复制redis到pika任务成功,任务执行结果留意如流通知");
    }

    @RequestMapping("/delHdztRedis")
    public Response delHdztRedis(Long actId, Long uid, String action, String seq, Long time, String sign) {
        log.info("delHdztRedis,seq:{}", seq);
        if (!checkManagerSign(time, action, actId, sign)) {
            log.warn("delHdztRedis wrong time:{}, action:{},actId:{},sign:{}", time, action, actId, sign);
            return Response.fail(-1, "para error");
        }
        try {
            redisDataTransferService.addDelRedisTask(actId, uid);
        } catch (Exception e) {
            return Response.fail(SuperException.E_FAIL, e.getMessage());
        }

        return Response.ok("发起删除redis热库数据成功,任务执行结果留意如流通知");
    }

    @RequestMapping("/checkActArchive")
    public Response checkActArchive(Long actId, Long uid, String action, String seq, Long time, String sign) {
        log.info("checkActArchive,seq:{},uid:{}", seq, uid);
        if (!checkManagerSign(time, action, actId, sign)) {
            log.warn("checkActArchive wrong time:{}, action:{},actId:{},sign:{}", time, action, actId, sign);
            return Response.fail(-1, "para error");
        }

        try {
            boolean isDone = redisDataTransferService.isDoneArchive(actId);
            if (isDone) {
                return Response.ok();
            }
        } catch (Exception e) {
            return Response.fail(SuperException.E_FAIL, e.getMessage());
        }

        return Response.fail(SuperException.E_FAIL, "not done yet");
    }

    @RequestMapping("/checkActDelRedis")
    public Response checkActDelRedis(Long actId, Long uid, String action, String seq, Long time, String sign) {
        log.info("checkActDelRedis,seq:{},uid:{}", seq, uid);
        if (!checkManagerSign(time, action, actId, sign)) {
            log.warn("checkActDelRedis wrong time:{}, action:{},actId:{},sign:{}", time, action, actId, sign);
            return Response.fail(-1, "para error");
        }

        try {
            boolean isDone = redisDataTransferService.isDoneDelRedis(actId);
            if (isDone) {
                return Response.ok();
            }
        } catch (Exception e) {
            return Response.fail(SuperException.E_FAIL, e.getMessage());
        }

        return Response.fail(SuperException.E_FAIL, "not done yet");
    }

    @RequestMapping("/setActArchive")
    public Response setActArchive(Long actId, Long uid, String action, String seq, Long time, String sign) {
        log.info("setActArchive,seq:{},uid:{}", seq, uid);
        if (!checkManagerSign(time, action, actId, sign)) {
            log.warn("setActArchive wrong time:{}, action:{},actId:{},sign:{}", time, action, actId, sign);
            return Response.fail(-1, "para error");
        }
        try {
            redisDataTransferService.setActStatusArchive(actId);
        } catch (Exception e) {
            return Response.fail(SuperException.E_FAIL, e.getMessage());
        }

        return Response.ok("设置归档状态成功，本活动redis所有redis操作将指向pika");
    }


}
