package com.yy.hdzt.ranking.report;

import io.opentelemetry.api.baggage.Baggage;
import io.opentelemetry.api.baggage.BaggageBuilder;
import io.opentelemetry.context.Context;
import io.opentelemetry.context.Scope;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * desc: OpenTelemetry 扩展服务，用于设置和管理 otel 上下文信息
 *
 * <AUTHOR>
 * @date 2025-08-05 16:39
 **/
public class OtelExtendService {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    // 上下文键常量
    private static final String TRACE_BEGIN_TIME_KEY = "traceBeginTime";
    private static final String SCENE_KEY = "scene";

    /**
     * 设置 trace 开始时间到 otel 上下文中
     *
     * @param traceBeginTime trace 开始时间（毫秒时间戳）
     * @return 返回新的 Context，需要使用 try-with-resources 来管理 Scope
     */
    public static Context setTraceBeginTime(long traceBeginTime) {
        return setTraceBeginTime(String.valueOf(traceBeginTime));
    }

    /**
     * 设置 trace 开始时间到 otel 上下文中
     *
     * @param traceBeginTime trace 开始时间字符串
     * @return 返回新的 Context，需要使用 try-with-resources 来管理 Scope
     */
    public static Context setTraceBeginTime(String traceBeginTime) {
        Baggage currentBaggage = Baggage.fromContext(Context.current());
        Baggage newBaggage = currentBaggage.toBuilder()
                .put(TRACE_BEGIN_TIME_KEY, traceBeginTime)
                .build();
        return Context.current().with(newBaggage);
    }

    /**
     * 设置场景信息到 otel 上下文中
     *
     * @param scene 场景信息
     * @return 返回新的 Context，需要使用 try-with-resources 来管理 Scope
     */
    public static Context setScene(String scene) {
        Baggage currentBaggage = Baggage.fromContext(Context.current());
        Baggage newBaggage = currentBaggage.toBuilder()
                .put(SCENE_KEY, scene)
                .build();
        return Context.current().with(newBaggage);
    }

    /**
     * 同时设置 trace 开始时间和场景信息到 otel 上下文中
     *
     * @param traceBeginTime trace 开始时间（毫秒时间戳）
     * @param scene 场景信息
     * @return 返回新的 Context，需要使用 try-with-resources 来管理 Scope
     */
    public static Context setTraceBeginTimeAndScene(long traceBeginTime, String scene) {
        return setTraceBeginTimeAndScene(String.valueOf(traceBeginTime), scene);
    }

    /**
     * 同时设置 trace 开始时间和场景信息到 otel 上下文中
     *
     * @param traceBeginTime trace 开始时间字符串
     * @param scene 场景信息
     * @return 返回新的 Context，需要使用 try-with-resources 来管理 Scope
     */
    public static Context setTraceBeginTimeAndScene(String traceBeginTime, String scene) {
        Baggage currentBaggage = Baggage.fromContext(Context.current());
        BaggageBuilder builder = currentBaggage.toBuilder();

        if (traceBeginTime != null) {
            builder.put(TRACE_BEGIN_TIME_KEY, traceBeginTime);
        }
        if (scene != null) {
            builder.put(SCENE_KEY, scene);
        }

        Baggage newBaggage = builder.build();
        return Context.current().with(newBaggage);
    }

    /**
     * 获取当前上下文中的 trace 开始时间
     *
     * @return trace 开始时间字符串，如果不存在则返回 null
     */
    public static String getTraceBeginTime() {
        Baggage baggage = Baggage.fromContext(Context.current());
        return baggage.getEntryValue(TRACE_BEGIN_TIME_KEY);
    }

    /**
     * 获取当前上下文中的 trace 开始时间（长整型）
     *
     * @return trace 开始时间毫秒时间戳，如果不存在或解析失败则返回 -1
     */
    public static long getTraceBeginTimeLong() {
        String timeStr = getTraceBeginTime();
        if (timeStr == null || timeStr.isEmpty()) {
            return -1L;
        }
        try {
            return Long.parseLong(timeStr);
        } catch (NumberFormatException e) {
            return -1L;
        }
    }

    /**
     * 获取当前上下文中的场景信息
     *
     * @return 场景信息字符串，如果不存在则返回 null
     */
    public static String getScene() {
        Baggage baggage = Baggage.fromContext(Context.current());
        return baggage.getEntryValue(SCENE_KEY);
    }

    /**
     * 使用指定的上下文执行操作
     * 这是一个便利方法，用于在特定上下文中执行代码块
     *
     * @param context 要使用的上下文
     * @param runnable 要执行的操作
     */
    public static void runWithContext(Context context, Runnable runnable) {
        try (Scope scope = context.makeCurrent()) {
            runnable.run();
        }
    }

    /**
     * 便利方法：设置 trace 开始时间并执行操作
     *
     * @param traceBeginTime trace 开始时间（毫秒时间戳）
     * @param runnable 要执行的操作
     */
    public static void runWithTraceBeginTime(long traceBeginTime, Runnable runnable) {
        Context context = setTraceBeginTime(traceBeginTime);
        runWithContext(context, runnable);
    }

    /**
     * 便利方法：设置场景信息并执行操作
     *
     * @param scene 场景信息
     * @param runnable 要执行的操作
     */
    public static void runWithScene(String scene, Runnable runnable) {
        Context context = setScene(scene);
        runWithContext(context, runnable);
    }

    /**
     * 便利方法：设置 trace 开始时间和场景信息并执行操作
     *
     * @param traceBeginTime trace 开始时间（毫秒时间戳）
     * @param scene 场景信息
     * @param runnable 要执行的操作
     */
    public static void runWithTraceBeginTimeAndScene(long traceBeginTime, String scene, Runnable runnable) {
        Context context = setTraceBeginTimeAndScene(traceBeginTime, scene);
        runWithContext(context, runnable);
    }
}
