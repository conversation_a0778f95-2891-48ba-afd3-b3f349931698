package com.yy.hdzt.ranking.support;

import com.google.common.collect.Lists;
import com.yy.hdzt.common.exception.SuperException;
import com.yy.hdzt.common.model.hdzt.RankingConfig;
import com.yy.hdzt.common.model.hdzt.RankingPhase;
import com.yy.hdzt.common.utils.DateUtil;
import com.yy.hdzt.common.utils.StringUtil;
import com.yy.hdzt.ranking.bean.RankKeyVo;
import com.yy.hdzt.ranking.commons.HdztConst;
import com.yy.hdzt.ranking.service.HdztActivityService;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.List;

/**
 * 时间分榜集中处理类
 */
public class TimeKeyHelper {

    // 时间分榜: 0-不按时间再分, 1-按日再分，2-按小时再分, 3-按周分，4-按月分, 5-按季，6-按年，7-15分钟榜，8-30分钟榜
    public static final long TIME_KEY_NO = 0;
    public static final long TIME_KEY_BY_DAY = 1;
    public static final long TIME_KEY_BY_HOUR = 2;
    public static final long TIME_KEY_BY_WEEK = 3;
    public static final long TIME_KEY_BY_MONTH = 4;
    public static final long TIME_KEY_BY_SEASON = 5;
    public static final long TIME_KEY_BY_YEAR = 6;

    /**
     * 注意，本次需求升级，15分钟和30分钟榜只验证了累榜和榜单结束事件
     * ！！！！！其他复杂结算pk联动等场景未经验证，使用的时候需要确认是否可用！！！！！
     */
    public static final long TIME_KEY_BY_15_MIN = 7;

    public static final long TIME_KEY_BY_30_MIN = 8;

    /**
     * 查找离 now 最近时段的 开始时间
     * 1）若第一个时段都没开始，则返回第一个时段的开始时间
     * 2）若已开始且正在考察时段中 或 在这个考察时段紧跟的空隙中，则返回这个考察时段的开始时间
     * 3）若所有时段都已结束，则返回最后一个时段的开始时间
     *
     * @param ranking - 榜单对象（不可null）
     * @param phase - 阶段对象（可null)
     * @param now - 当前时刻
     */
    public static Date findLastBeginTimeForNotify(RankingConfig ranking, RankingPhase phase, Date now, List<String> slotInHour) {
        // 先准备时间序列， 若为空，则返回 calcBeginTime
        List<Date[]> timeList = getTimePeriodList(ranking, phase, slotInHour);
        if(timeList == null) {
            return getCalcBeginTime(ranking, phase);
        }

        // 遍历时间序列，找到 now 所处的时段位置（精确到秒来计算）
        // 1. 若第一个时段都还没有开始，返回null
        long nowSeconds = now.getTime() / 1000;
        if(notBegin(timeList, nowSeconds)) {
            return null;
        }

        // 2. 查看是否在某个时段中
        int size = timeList.size();
        for(int i = 0; i< size; i++) {
            // 准备 i 时段的开始秒 和 结束秒
            Date[] time = timeList.get(i);
            long timeBeginSeconds = time[0].getTime() / 1000;
            long timeEndSeconds = time[1].getTime() / 1000;

            // 刚好在 i 时段中，说明 i 时段已开始且未结束
            if(nowSeconds >= timeBeginSeconds && nowSeconds <= timeEndSeconds) {
                return time[0];
            }

            // 若不在 i 时段中，但在 i ~ i+1 之间的空隙中，则 i 时段肯定已结束了，但 i+1 阶段尚未开始 - added by guoliping / 2023-01-09
            if(hasTimeGap(timeList, nowSeconds, i)) {
                return null;
            }
        }

        // 3. 则表明已结束了所有时段，返回null
        return null;
    }

    /**
     * 查找离 now 最近时段的 结束时间
     * 1）若第一个时段都没开始，则返回第一个时段的结束时间
     * 2）若已开始且正在考察时段中，则返回其紧接的上个时段的结束时间（若考察时段是第一个时段，则返回考察时段的结束时间）
     * 3）若已开始且不在考察时段中，但在紧跟的空隙中且考察时段不是最后一个时段，则返回 考察时段 的结束时间
     * 4）若所有时段都已结束，则返回最后一个时段的结束时间
     *
     * @param ranking - 榜单对象（不可null）
     * @param phase - 阶段对象（可null)
     * @param now - 当前时刻
     */
    public static Date findLastEndTimeForNotify(RankingConfig ranking, RankingPhase phase, Date now, List<String> slotInHour) {
        // 先准备时间序列， 若为空，则返回 calcEndTime
        List<Date[]> timeList = getTimePeriodList(ranking, phase, slotInHour);
        if(timeList == null) {
            return getCalcEndTime(ranking, phase);
        }

        // 遍历时间序列，找到 now 所处的位置（精确到秒来计算）
        // 1. 若第一个时段都还没有开始，返回null
        long nowSeconds = now.getTime() / 1000;
        if(notBegin(timeList, nowSeconds)) {
            return null;
        }

        // 2. 查看是否在某个时段中
        int size = timeList.size();
        for(int i = 0; i< size; i++) {
            // 准备 i 时段的开始秒 和 结束秒
            Date[] time = timeList.get(i);
            long timeBeginSeconds = time[0].getTime() / 1000;
            long timeEndSeconds = time[1].getTime() / 1000;

            // 刚好在 i 时段中
            if(nowSeconds >= timeBeginSeconds && nowSeconds <= timeEndSeconds) {
                // 若是第一个时段则没有结束事件，返回 null， 否则返回其上个时段的结束时间(可能跨了和上个时段间空隙，必须如此的，因空隙或很短，这样通知更保险）
                return (i == 0)  ? null : timeList.get(i - 1)[1];
            }

            // 若不在 i 时段中，但在 i ~ i+1 之间的空隙中，则 i 时段肯定已结束了， 返回 i 时段的结束时间
            if(hasTimeGap(timeList, nowSeconds, i)) {
                return time[1];
            }
        }

        // 3. 则表明已结束了所有时段，返回最后一个时段的结束时间
        return timeList.get(size-1)[1];
    }

    /**
     * 判断第一个时段有没开始
     * @param timeList - 所有时段的列表（按开始时间排序），边界由调用者保证
     * @param nowSeconds - 当前的秒数
     */
    private static boolean notBegin(List<Date[]> timeList, long nowSeconds) {
        if(CollectionUtils.isEmpty(timeList)) {
            return true;
        }
        long firstBeginTimeSeconds = timeList.get(0)[0].getTime() / 1000;
        return nowSeconds < firstBeginTimeSeconds;
    }

    /**
     * 判断 pos ~ pos+1 时段之间有没空隙，边界条件由调用者自己保证
     *
     * @param timeList - 所有时段
     * @param nowSeconds - 当前时刻秒数
     * @param pos - 正在考察时段的位置（下标从0开始）
     */
    private static boolean hasTimeGap(List<Date[]> timeList, long nowSeconds, int pos) {
        // 是最后一个时段肯定无空隙
        if(pos >= timeList.size() -1) {
            return false;
        }
        long timeEndSeconds = timeList.get(pos)[1].getTime() / 1000;
        long nextBeginSeconds = timeList.get(pos+1)[0].getTime() / 1000;
        return nowSeconds > timeEndSeconds && nowSeconds < nextBeginSeconds;
    }

    /**
     * 检查时间分榜策略
     *
     * 目前只有小时榜考虑了开始时间大于结束时间的跨天情况，其他按时间分榜策略暂不支持这样配置！！！！
     */
    public static boolean checkTimeSplitPolicy(String currTimeStr, RankingConfig ranking, boolean bCheckOther) {
        long timeKey = ranking.getTimeKey();
        String timeKeyBegin = StringUtil.trim(ranking.getTimeKeyBegin());
        String timeKeyEnd = StringUtil.trim(ranking.getTimeKeyEnd());
        timeKeyBegin =  timeKeyBegin.isEmpty() ? "00:00:00" : timeKeyBegin;
        timeKeyEnd =  timeKeyEnd.isEmpty() ? "23:59:59" : timeKeyEnd;

        // 时间区间跨天时， 只有小时分榜才可以（无分榜 和 其它时间分榜都不可以）
        if(timeKeyBegin.compareTo(timeKeyEnd) > 0 && !isSplitByHour(timeKey)) {
            throw new SuperException("只有按小時分榜的开始时间才能超过结束时间", 9999);
        }

        boolean bHourOrDay = TimeKeyHelper.isSplitByHour(timeKey) || TimeKeyHelper.isSplitByDay(timeKey);

        // 按小时和按日一定要检查，其它根据
        if(bHourOrDay || bCheckOther) {
            // 若当前时间不在日时间要求范围的，返回失败
            if (timeKeyBegin.compareTo(timeKeyEnd) > 0) {
                // 跨天情况（开始时间大于结束时间）
                boolean flag1 = currTimeStr.compareTo(timeKeyBegin) >= 0 && currTimeStr.compareTo("23:59:59") <= 0;
                boolean flag2 = currTimeStr.compareTo("00:00:00") >= 0 && currTimeStr.compareTo(timeKeyEnd) <= 0;
                if (!flag1 && !flag2) {
                    return false;
                }
            } else {
                // 无跨天情况
                if (currTimeStr.compareTo(timeKeyBegin) < 0 || currTimeStr.compareTo(timeKeyEnd) > 0) {
                    return false;
                }
            }
        }

        return true;
    }

    /**
     * 获取时间分榜子Key
     */
    public static String getTimeSubKey(long timeKey, Date date) {
        String timeCode = getTimeCode(timeKey, date);
        return StringUtil.isBlank(timeCode) ? "ALL" : timeCode;
    }

    /**
     * 对按时间分榜的，返回当时间分榜对应的计榜开始/结束时刻，否则返回总榜的开始/结束时刻
     */
    public static Date adjustTimeForPhaseSettle(RankingConfig ranking, Date calcTime, Date now, boolean bBegin) {
        // 非日榜和小时榜的，直接返回原时间
        long timeKeyFlag = ranking.getTimeKey();
        if(!(isSplitByHour(timeKeyFlag) || isSplitByDay(timeKeyFlag))) {
            return calcTime;
        }

        // 对日榜和小时榜做特别处理
        String timeKeyBegin = StringUtil.trim(ranking.getTimeKeyBegin());
        String timeKeyEnd = StringUtil.trim(ranking.getTimeKeyEnd());
        String timeKeyTime = bBegin ? timeKeyBegin : timeKeyEnd;
        timeKeyTime = timeKeyTime.isEmpty() ? (bBegin ? "00:00:00" : "23:59:59") : timeKeyTime;
        if(timeKeyBegin.compareTo(timeKeyEnd) <= 0) {
            return calcNormalHourDayTime(ranking, timeKeyFlag, timeKeyTime, calcTime, now, bBegin);
        } else {
            return calcAbnormalHourTime(ranking, calcTime, now, bBegin, timeKeyBegin, timeKeyEnd, timeKeyTime);
        }
    }

    private static Date calcAbnormalHourTime(RankingConfig ranking, Date calcTime, Date now, boolean bBegin, String timeKeyBegin, String timeKeyEnd, String timeKeyTime) {
        // 没开始 或 已结束，返回 calcTime 本身
        boolean flag = bBegin && now.before(calcTime) || !bBegin && now.after(calcTime);
        if(flag) {
            return calcTime;
        }

        // 否则必然是按小时且跨天（开始时间大于结束时间）
        String day = DateUtil.format(calcTime, "yyyy-MM-dd");
        if(isFirstHour(timeKeyBegin, calcTime, now)) {
            String hms = bBegin ? DateUtil.format(calcTime, "HH:mm:ss") : DateUtil.format(calcTime, "HH:59:59");
            String adjust = adjustTime(timeKeyTime, bBegin, hms);
            return DateUtil.getDate(day + " " + adjust);
        } else if(isWholeHour(timeKeyBegin, timeKeyEnd, calcTime, now)) {
            String pattern = bBegin ? "yyyy-MM-dd HH:00:00" : "yyyy-MM-dd HH:59:59";
            return DateUtil.getDate(DateUtil.format(now, pattern));
        } else {
            // 最后一个小时（不在时间范围内也当是最后一个小时）
            String hms = bBegin ? DateUtil.format(calcTime, "HH:00:00") : DateUtil.format(calcTime, "HH:mm:ss");
            String adjust = adjustTime(timeKeyTime, bBegin, hms);
            return DateUtil.getDate(day + " " + adjust);
        }
    }

    /**
     * 提示：本函数只能处理不跨天的情况！
     * 对按时间分榜的，返回当时间分榜对应的计榜开始/结束时刻，否则返回总榜的开始/结束时刻
     */
    private static Date calcNormalHourDayTime(RankingConfig ranking, long timeKeyFlag, String timeKeyTime, Date calcTime, Date now, boolean bBegin) {
        // 判断是否超过了正常时间区间
        String strCalcTime = DateUtil.format(calcTime, "HH:mm:ss");
        String strCalcDate = DateUtil.format(calcTime, "yyyy-MM-dd ");
        if(bBegin) {
            String tmp = strCalcTime.compareTo(timeKeyTime) > 0 ? strCalcTime : timeKeyTime;
            Date tmpTime = DateUtil.getDate(strCalcDate + tmp);
            if(tmpTime.getTime() >= now.getTime()) {
                return tmpTime;
            }
        } else {
            String tmp = strCalcTime.compareTo(timeKeyTime) < 0 ? strCalcTime : timeKeyTime;
            Date tmpTime = DateUtil.getDate(strCalcDate + tmp);
            if(tmpTime.getTime() <= now.getTime()) {
                return tmpTime;
            }
        }

        // 计算时间和当前时间是同一天，比较时间分榜的开始/结束的 【时分秒】
        if(isSameDay(calcTime, now)) {
            timeKeyTime = adjustTime(timeKeyTime, bBegin, strCalcTime);
        }

        String resultTime = DateUtil.format(now, "yyyy-MM-dd ") + timeKeyTime;
        if(isSplitByHour(timeKeyFlag)) {
            // 第1个小时榜之后的小时榜已开始
            int hourCmp = timeKeyTime.substring(0, 2).compareTo(DateUtil.format(now, "HH"));
            if(bBegin) {
                if (hourCmp < 0) {
                    resultTime = DateUtil.format(now, "yyyy-MM-dd HH:00:00");
                }
            } else {
                if (hourCmp > 0) {
                    resultTime = DateUtil.format(now, "yyyy-MM-dd HH:59:59");
                }
            }
        }

        return DateUtil.getDate(resultTime);
    }

    /**
     * 获取分时相关的时间编码（yyyyMMdd 或者 yyyyMMddHH）
     */
    private static String getTimeCode(long timeKey, Date date) {
        String timeCode = null;
        if (isSplitByDay(timeKey)) {
            timeCode = DateUtil.format(date, DateUtil.PATTERN_TYPE2);
        } else if(isSplitByHour(timeKey)) {
            timeCode = DateUtil.format(date, DateUtil.PATTERN_TYPE7);
        } else if(isSplitByWeek(timeKey)) {
            timeCode = DateUtil.format(DateUtil.getWeekFirstSecond(date), DateUtil.PATTERN_TYPE2);
        } else if(isSplitByMonth(timeKey)) {
            timeCode = DateUtil.format(DateUtil.getMonthFirstSecond(date), DateUtil.PATTERN_TYPE2);
        } else if(isSplitBySeason(timeKey)) {
            timeCode = DateUtil.format(DateUtil.getSeasonFirstSecond(date), DateUtil.PATTERN_TYPE2);
        } else if(isSplitByYear(timeKey)) {
            timeCode = DateUtil.format(DateUtil.getYearFirstSecond(date), DateUtil.PATTERN_TYPE2);
        } else if (isSplitBy15Min(timeKey)) {
            timeCode = DateUtil.format(DateUtil.getDayOf15MinuteInterval(date), DateUtil.PATTERN_TYPE8);
        } else if (isSplitBy30Min(timeKey)) {
            timeCode = DateUtil.format(DateUtil.getDayOf30MinuteInterval(date), DateUtil.PATTERN_TYPE8);
        }
        return timeCode;
    }

    /**
     * 获取当前分时边界时间对象
     */
    public static Date getCurrTimeValue(boolean bBegin, long timeKeyFlag, Date now) {
        Date date = null;
        if(isSplitByHour(timeKeyFlag)) {
            date = bBegin ? DateUtil.getHourFirstSecond(now) : DateUtil.getHourLastSecond(now);
        } else if(isSplitByDay(timeKeyFlag)) {
            date = bBegin ? DateUtil.getDayFirstSecond(now) : DateUtil.getDayLastSecond(now);
        } else if(isSplitByWeek(timeKeyFlag)) {
            date = bBegin ? DateUtil.getWeekFirstSecond(now) : DateUtil.getWeekLastSecond(now);
        } else if(isSplitByMonth(timeKeyFlag)) {
            date = bBegin ? DateUtil.getMonthFirstSecond(now) : DateUtil.getMonthLastSecond(now);
        } else if(isSplitBySeason(timeKeyFlag)) {
            date = bBegin ? DateUtil.getSeasonFirstSecond(now) : DateUtil.getSeasonLastSecond(now);
        } else if(isSplitByYear(timeKeyFlag)) {
            date = bBegin ? DateUtil.getYearFirstSecond(now) : DateUtil.getYearLastSecond(now);
        } else if (isSplitBy15Min(timeKeyFlag)) {
            date = bBegin ? DateUtil.getDayOf15MinuteFirstSeconds(now) : DateUtil.getDayOf15MinuteLastSeconds(now);
        } else if (isSplitBy30Min(timeKeyFlag)) {
            date = bBegin ? DateUtil.getDayOf30MinuteFirstSeconds(now) : DateUtil.getDayOf30MinuteLastSeconds(now);
        }
        return date;
    }

    /**
     * 获取上个分时周期边界时间对象
     */
    public static Date getPrevTimeValue(boolean bBegin, long timeKeyFlag, Date now) {
        Date date = null;
        if(isSplitByHour(timeKeyFlag)) {
            date = bBegin ? DateUtil.getPrevHourFirstSecond(now) : DateUtil.getPrevHourLastSecond(now);
        } else if(isSplitByDay(timeKeyFlag)) {
            date = bBegin ? DateUtil.getPrevDayFirstSecond(now) : DateUtil.getPrevDayLastSecond(now);
        } else if(isSplitByWeek(timeKeyFlag)) {
            date = bBegin ? DateUtil.getPrevWeekFirstSecond(now) : DateUtil.getPrevWeekLastSecond(now);
        } else if(isSplitByMonth(timeKeyFlag)) {
            date = bBegin ? DateUtil.getPrevMonthFirstSecond(now) : DateUtil.getPrevMonthLastSecond(now);
        } else if(isSplitBySeason(timeKeyFlag)) {
            date = bBegin ? DateUtil.getPrevSeasonFirstSecond(now) : DateUtil.getPrevSeasonLastSecond(now);
        } else if(isSplitByYear(timeKeyFlag)) {
            date = bBegin ? DateUtil.getPrevYearFirstSecond(now) : DateUtil.getPrevYearLastSecond(now);
        }else if(isSplitBy15Min(timeKeyFlag)){
            date = bBegin ? DateUtil.getDayOf15MinutePreFirstSeconds(now) : DateUtil.getDayOf15MinutePreLastSeconds(now);
        }else if(isSplitBy30Min(timeKeyFlag)){
            date = bBegin ? DateUtil.getDayOf30MinutePreFirstSeconds(now) : DateUtil.getDayOf30MinutePreLastSeconds(now);
        }
        return date;
    }

    /**
     * 获取下个分时周期边界时间对象
     */
    private static Date getNextTimeValue(boolean bBegin, long timeKeyFlag, Date now) {
        Date date = null;
        if(isSplitByHour(timeKeyFlag)) {
            date = bBegin ? DateUtil.getNextHourFirstSecond(now) : DateUtil.getNextHourLastSecond(now);
        } else if(isSplitByDay(timeKeyFlag)) {
            date = bBegin ? DateUtil.getNextDayFirstSecond(now) : DateUtil.getNextDayLastSecond(now);
        } else if(isSplitByWeek(timeKeyFlag)) {
            date = bBegin ? DateUtil.getNextWeekFirstSecond(now) : DateUtil.getNextWeekLastSecond(now);
        } else if(isSplitByMonth(timeKeyFlag)) {
            date = bBegin ? DateUtil.getNextMonthFirstSecond(now) : DateUtil.getNextMonthLastSecond(now);
        } else if(isSplitBySeason(timeKeyFlag)) {
            date = bBegin ? DateUtil.getNextSeasonFirstSecond(now) : DateUtil.getNextSeasonLastSecond(now);
        } else if(isSplitByYear(timeKeyFlag)) {
            date = bBegin ? DateUtil.getNextYearFirstSecond(now) : DateUtil.getNextYearLastSecond(now);
        }else if(isSplitBy15Min(timeKeyFlag)){
            date = bBegin ? DateUtil.getDayOf15MinuteNextFirstSeconds(now) : DateUtil.getDayOf15MinuteNextLastSeconds(now);
        }else if(isSplitBy30Min(timeKeyFlag)){
            date = bBegin ? DateUtil.getDayOf30MinuteNextFirstSeconds(now) : DateUtil.getDayOf30MinuteNextLastSeconds(now);
        }
        return date;
    }

    /**
     * 选取合适的时间：开始取晚者，结束取早者
     */
    public static String adjustTime(String flagTime, boolean bBegin, String time) {
        if (bBegin) {
            if (time.compareTo(flagTime) > 0) {
                flagTime = time;
            }
        } else {
            if (time.compareTo(flagTime) < 0) {
                flagTime = time;
            }
        }
        return flagTime;
    }

    /**
     * 判断是否同一天
     */
    public static boolean isSameDay(Date calcTime, Date now) {
        return DateUtil.format(calcTime, DateUtil.PATTERN_TYPE2).equals(DateUtil.format(now, DateUtil.PATTERN_TYPE2));
    }

    /**
     * 判断是否同一小时
     */
    public static boolean isSameHour(Date calcTime, Date now) {
        return DateUtil.format(calcTime, DateUtil.PATTERN_TYPE7).equals(DateUtil.format(now, DateUtil.PATTERN_TYPE7));
    }

    /**
     * 跨天时，判断是否在完整的小时内（去掉头、尾）
     */
    private static boolean isWholeHour(String timeKeyBegin, String timeKeyEnd, Date calcTime, Date now) {
        if(isFirstHour(timeKeyBegin, calcTime, now) || isLastHour(timeKeyEnd, calcTime, now)) {
            return false;
        }

        String nowHour = DateUtil.format(now, "HH");
        String calcTimeHour = DateUtil.format(calcTime, "HH");

        String timeKeyBeginHour = timeKeyBegin.substring(0, 2);
        String firstHour = timeKeyBeginHour.compareTo(calcTimeHour) > 0 ? timeKeyBeginHour : calcTimeHour;
        boolean bFirst = nowHour.compareTo(firstHour) > 0 && nowHour.compareTo("23")<=0;

        String timeKeyEndHour = timeKeyEnd.substring(0, 2);
        String lastHour = timeKeyEndHour.compareTo(calcTimeHour) < 0 ? timeKeyEndHour : calcTimeHour;
        boolean bLast = nowHour.compareTo("00") >= 0 && nowHour.compareTo(lastHour)<0;

        return bFirst || bLast;
    }

    /**
     * 跨天时，判断是否在最后一个小时
     */
    private static boolean isLastHour(String timeKeyEnd, Date calcTime, Date now) {
        String timeKeyEndHour = timeKeyEnd.substring(0, 2);
        String calcTimeHour = DateUtil.format(calcTime, "HH");
        String nowHour = DateUtil.format(now, "HH");
        String lastHour = timeKeyEndHour.compareTo(calcTimeHour) < 0 ? timeKeyEndHour : calcTimeHour;
        return nowHour.equals(lastHour);
    }

    /**
     * 跨天时，判断是否在第一个小时
     */
    private static boolean isFirstHour(String timeKeyBegin, Date calcTime, Date now) {
        String timeKeyBeginHour = timeKeyBegin.substring(0, 2);
        String calcTimeHour = DateUtil.format(calcTime, "HH");
        String nowHour = DateUtil.format(now, "HH");
        String firstHour = timeKeyBeginHour.compareTo(calcTimeHour) > 0 ? timeKeyBeginHour : calcTimeHour;
        return nowHour.equals(firstHour);
    }

    /**
     * 增加时间分榜子Key
     */
    public static String addTimeSubKey(RankingConfig ranking, Date now, String key) {
        long timeKey = ranking.getTimeKey();
        String timeCode = getTimeCode(timeKey, now);
        String subKey = StringUtil.isBlank(timeCode) ? HdztConst.RANKING_KEY_MEMBER_SEPERATOR : timeCode;
        return key + subKey;
    }

    /**
     * 添加时间分榜子key
     */
    public static void addTimeSubKeyForQuery(RankingConfig ranking, Date now, List<String> result, String key) {
        Long timeKey = ranking.getTimeKey();
        if (TimeKeyHelper.notTimeSplit(timeKey)) {
            result.add(key + HdztConst.RANKING_KEY_MEMBER_SEPERATOR);
            return;
        }

        // TODO::待自己从  hdzt_activity 中组织日期
        if (now == null) {
            return;
        }

        String timeCode = getTimeCode(timeKey, now);
        if(!StringUtil.isBlank(timeCode)) {
            result.add(key + timeCode);
        }
    }

    /**
     * 根据时间分榜标记生成榜单key
     */
    public static void makeRankKeyByTimeSplit(RankingConfig ranking, Date now, List<RankKeyVo> result, long rankId, String key, HdztActivityService hdztActivityService) {
        Long timeKey = ranking.getTimeKey();
        RankKeyVo rankKeyVo = new RankKeyVo();
        rankKeyVo.setRankId(rankId);

        if(notTimeSplit(timeKey)) {
            rankKeyVo.setRankKey(key + HdztConst.RANKING_KEY_MEMBER_SEPERATOR);
            result.add(rankKeyVo);
        } else {
            if(now == null) {
                List<RankKeyVo> hourRankKey = makeTimeRankKey(ranking, key, hdztActivityService);
                if(!CollectionUtils.isEmpty(hourRankKey)){
                    result.addAll(hourRankKey);
                }
            } else {
                String timeCode = getTimeCode(timeKey, now);
                if(!StringUtil.isBlank(timeCode)) {
                    rankKeyVo.setRankKey(key + timeCode + ":" + HdztConst.RANKING_KEY_MEMBER_SEPERATOR);
                    rankKeyVo.setDayCode(timeCode);
                    result.add(rankKeyVo);
                }
            }
        }
    }

    /**
     * 制作时间分榜key
     */
    public static List<RankKeyVo> makeTimeRankKey(RankingConfig ranking, String keyPre, HdztActivityService hdztActivityService) {
        List<RankKeyVo> rankKeyVos = Lists.newArrayList();
        Date beginDate = ranking.getCalcBeginTime();
        Date endDate = hdztActivityService.getCurrSysTime(ranking.getActId());
        Date curDate = DateUtil.getDayFirstSecond(beginDate);
        Long timeKey = ranking.getTimeKey();
        while (curDate.before(endDate)) {
            String timeCode = getTimeCode(timeKey, curDate);

            // 跳过非采榜范围内的小时榜  TODO ? 这里是否要加上15分钟榜、30分钟榜
            if (isSplitByHour(timeKey)) {
                long time = curDate.getTime();
                if (time < beginDate.getTime() || time > endDate.getTime()) {
                    timeCode = null;
                }
            }

            if (timeCode != null) {
                RankKeyVo vo = new RankKeyVo();
                vo.setRankId(ranking.getRankId());
                vo.setDayCode(timeCode);
                vo.setRankKey(keyPre + timeCode + ":" + HdztConst.RANKING_KEY_MEMBER_SEPERATOR);
                rankKeyVos.add(vo);
            }

            curDate = getNextTimeValue(true, timeKey, curDate);
        }

        return rankKeyVos;
    }

    /**
     * 现在主要用于榜单开始、结束事件通知
     * 获取时间段列表（按开始时间排序，时段之间没交集，时段之间允许有时间空隙）
     * @param ranking - 榜单对象（不可null）
     * @param phase - 阶段对象（可为null）
     * @return
     */
    public static List<Date[]> getTimePeriodList(RankingConfig ranking, RankingPhase phase, List<String> slotInHour) {
        // 不是日榜 和 小时榜， 返回null，提示调用者 返回原来的计榜开始/结束时间
        long timeKeyFlag = ranking.getTimeKey();
        if(notTimeSplit(timeKeyFlag)) {
            return null;
        }

        Date calcBeginTime = getCalcBeginTime(ranking, phase);
        Date calcEndTime = getCalcEndTime(ranking, phase);
        String timeKeyBegin = ranking.getTimeKeyBegin();
        String timeKeyEnd = ranking.getTimeKeyEnd();

        // 先准备时间序列
        List<Date[]> timeList = null;
        if (isSplitByHour(timeKeyFlag)) {
            timeList = makeTimePeriodListForHour(calcBeginTime, calcEndTime, timeKeyBegin, timeKeyEnd, slotInHour);
        } else if (isSplitBy15Min(timeKeyFlag) || isSplitBy30Min(timeKeyFlag)) {
            timeList = makeTimePeriodListForMin(timeKeyFlag, calcBeginTime, calcEndTime, timeKeyBegin, timeKeyEnd);
        } else {
            timeList = makeTimePeriodListForOther(timeKeyFlag, calcBeginTime, calcEndTime, timeKeyBegin, timeKeyEnd);
        }


        return timeList;
    }

    /**
     * 计算开始时间：取更晚者
     */
    public static Date getCalcBeginTime(RankingConfig ranking, RankingPhase phase) {
        Date calcBeginTime = ranking.getCalcBeginTime();
        if(phase != null) {
            if(phase.getBeginTime().after(calcBeginTime)) {
                calcBeginTime = phase.getBeginTime();
            }
        }
        return calcBeginTime;
    }

    /**
     * 计算结束时间：取更早者
     */
    public static Date getCalcEndTime(RankingConfig ranking, RankingPhase phase) {
        Date calcEndTime = ranking.getCalcEndTime();
        if(phase != null) {
            if(phase.getEndTime().before(calcEndTime)) {
                calcEndTime = phase.getEndTime();
            }
        }
        return  calcEndTime;
    }

    /**
     * 对除了小时外的时榜 按时间顺序制作时间片段序列
     */
    private static List<Date[]> makeTimePeriodListForOther(long timeKeyFlag, Date calcBeginTime, Date calcEndTime, String timeKeyBegin, String timeKeyEnd) {
        if(notTimeSplit(timeKeyFlag) || isSplitByHour(timeKeyFlag)) {
            return null;
        }

        Date from = getCurrTimeValue(true, timeKeyFlag, calcBeginTime);
        Date to = getCurrTimeValue(false, timeKeyFlag, calcEndTime);
        String beginPeriod =  DateUtil.format(from, DateUtil.PATTERN_TYPE5);
        String endPeriod = DateUtil.format(to, DateUtil.PATTERN_TYPE5);

        // 规整化时间分榜的开始、结束的 时分秒
        timeKeyBegin = StringUtil.isBlank(timeKeyBegin) ? "00:00:00" : timeKeyBegin.trim();
        timeKeyEnd =  StringUtil.isBlank(timeKeyEnd) ? "23:59:59" : timeKeyEnd.trim();

        String currPeriod = beginPeriod;
        List<Date[]> list = Lists.newArrayList();
        
        while(currPeriod.compareTo(endPeriod) <= 0) {
            Date periodHead = DateUtil.getDate(currPeriod, DateUtil.PATTERN_TYPE5);
            Date periodTail = getCurrTimeValue(false, timeKeyFlag, periodHead);

            // 先添加日时间区间 HH:mm:ss 部分
            Date begin = DateUtil.getDate(DateUtil.format(periodHead, DateUtil.PATTERN_TYPE5 + " " + timeKeyBegin));
            Date end = DateUtil.getDate(DateUtil.format(periodTail, DateUtil.PATTERN_TYPE5 + " " + timeKeyEnd));

            // 计榜时间范围内的第一个周期，还要考察 calcBeginTime 的时分秒
            if(isSameTimePeriod(periodHead, beginPeriod, DateUtil.PATTERN_TYPE5) && calcBeginTime.after(begin)) {
                begin = calcBeginTime;
            }

            // 计榜时间范围内的最后一个周期，还要考察 calcEndTime 的时分秒
            if(isSameTimePeriod(periodTail, endPeriod, DateUtil.PATTERN_TYPE5) && calcEndTime.before(end)) {
                end = calcEndTime;
            }

            list.add(new Date[] {begin, end});

            // 调整到下个周期1号
            currPeriod =  DateUtil.format(getNextTimeValue(true, timeKeyFlag, periodHead), DateUtil.PATTERN_TYPE5);
        }

        return list;
    }

    /**
     * 对小时榜按时间顺序制作时间片段序列
     * 1. 时间段的开始时间以下面规则确定
     *      a) 若是每天第一个小时段，取 timeKeyBegin
     *      b) 若是第一天第一个小时段，取 max(calcBeginTime, timeKeyBegin)
     *      c) 否则取 ${HH}:00:00
     * 2. 时间段的结束时间以下面规则确定
     *      a) 若是每天最后一个小时段，取 timeKeyEnd
     *      b) 若是最后一天最后一个小时段，取 min(calcEndTime, timeKeyEnd)
     *      c) 否则取 ${HH}:59:59
     */
    private static List<Date[]> makeTimePeriodListForHour(Date calcBeginTime, Date calcEndTime, String timeKeyBegin, String timeKeyEnd, List<String> slotInHour) {
        String pattern = "yyyy-MM-dd HH";
        String beginHour = DateUtil.format(calcBeginTime, pattern);
        String endHour = DateUtil.format(calcEndTime, pattern);

        // 规整化时间分榜的开始、结束的 时分秒
        timeKeyBegin = StringUtil.isBlank(timeKeyBegin) ? "00:00:00" : timeKeyBegin.trim();
        timeKeyEnd =  StringUtil.isBlank(timeKeyEnd) ? "23:59:59" : timeKeyEnd.trim();
        String timeKeyBeginHour = timeKeyBegin.substring(0, 2);
        String timeKeyEndHour = timeKeyEnd.substring(0, 2);

        int inx = 0;
        String nowHour = beginHour;
        Date baseHour = DateUtil.getDate(beginHour, pattern);
        List<Date[]> list = Lists.newArrayList();

        while(nowHour.compareTo(endHour) <= 0) {
            String hour = nowHour.substring(pattern.length() - 2);
            if(isValidHour(timeKeyBeginHour, timeKeyEndHour, hour)) {
                // 先假定是中间的完整小时, 以时间分榜指定的时间为基准
                Date begin = DateUtil.getDate(nowHour + ":00:00");
                Date end = DateUtil.getDate(nowHour + ":59:59");

                // 若当前小时是每天小时榜区段的第一个小时
                if(hour.equals(timeKeyBeginHour)) {
                    begin = DateUtil.getDate(nowHour + timeKeyBegin.substring(2));
                }

                // 若当前小时是每天小时榜区段的第最后一个小时（跨了天的也是这样算）
                if(hour.equals(timeKeyEndHour)) {
                    end = DateUtil.getDate(nowHour + timeKeyEnd.substring(2));
                }

                // 计榜时间范围内的第一个小时，还要考察 calcBeginTime 的 分秒
                if (nowHour.equals(beginHour) && calcBeginTime.after(begin)) {
                    begin = calcBeginTime;
                }

                // 计榜时间范围内的最后一个小时，还要考察 calcEndTime 的 分秒
                if (nowHour.equals(endHour) && calcEndTime.before(end)) {
                    end = calcEndTime;
                }

                // 小时内的时间槽调整，[begin, end] 交 [left, right]
                if (slotInHour != null && slotInHour.size() == 2) {
                    String left = slotInHour.get(0);
                    String right = slotInHour.get(1);
                    String format = "yyyy-MM-dd HH:";
                    if (DateUtil.format(begin, "mm:ss").compareTo(left) < 0) {
                        begin = DateUtil.getDate(DateUtil.format(begin, format) + left);
                    }
                    if (DateUtil.format(end, "mm:ss").compareTo(right) > 0) {
                        end = DateUtil.getDate(DateUtil.format(end, format) + right);
                    }
                }

                list.add(new Date[]{begin, end});
            }
            nowHour = DateUtil.format(DateUtil.addHours(baseHour, ++inx), pattern);
        }

        return list;
    }

    /**
     * 对15/30 分钟分榜 按时间顺序制作时间片段序列
     */
    private static List<Date[]> makeTimePeriodListForMin(long timeKeyFlag, Date calcBeginTime, Date calcEndTime, String timeKeyBegin, String timeKeyEnd) {
        if (notTimeSplit(timeKeyFlag) || isSplitByHour(timeKeyFlag)) {
            return null;
        }

        Date from = getCurrTimeValue(true, timeKeyFlag, calcBeginTime);
        Date to = getCurrTimeValue(false, timeKeyFlag, calcEndTime);

        // 规整化时间分榜的开始、结束的 时分秒
        timeKeyBegin = StringUtil.isBlank(timeKeyBegin) ? "00:00:00" : timeKeyBegin.trim();
        timeKeyEnd = StringUtil.isBlank(timeKeyEnd) ? "23:59:59" : timeKeyEnd.trim();

        Date currPeriod = from;
        List<Date[]> list = Lists.newArrayList();

        while (currPeriod.getTime() <= to.getTime()) {
            Date periodHead = currPeriod;
            Date periodTail = getCurrTimeValue(false, timeKeyFlag, periodHead);

            // 当天的开始结束时间
            Date dayBegin = DateUtil.getDate(DateUtil.format(periodHead, DateUtil.PATTERN_TYPE5 + " " + timeKeyBegin));
            Date dayEnd = DateUtil.getDate(DateUtil.format(periodTail, DateUtil.PATTERN_TYPE5 + " " + timeKeyEnd));

            //不在当前约束的时间范围内
            if (currPeriod.getTime() >= dayBegin.getTime() && currPeriod.getTime() <= dayEnd.getTime()) {
                Date resultBegin = periodHead;
                Date resultTail = periodTail;

                // 计榜时间范围内的第一个周期，还要考察 calcBeginTime 的时分秒
                if (periodHead.getTime() == from.getTime() && calcBeginTime.after(dayBegin)) {
                    resultBegin = calcBeginTime;
                }
                // 计榜时间范围内的最后一个周期，还要考察 calcEndTime 的时分秒
                if (periodTail.getTime() == to.getTime() && calcEndTime.before(dayEnd)) {
                    resultTail = calcEndTime;
                }

                list.add(new Date[]{resultBegin, resultTail});
            }

            // 调整到下个周期1号
            currPeriod = DateUtil.addMinutes(periodHead, getPeriodTimeMinGap(timeKeyFlag));
        }

        return list;
    }

    private static int getPeriodTimeMinGap(long timeKeyFlag) {
        if (TIME_KEY_BY_15_MIN == timeKeyFlag) {
            return 15;
        } else if (TIME_KEY_BY_30_MIN == timeKeyFlag) {
            return 30;
        }
        throw new RuntimeException("getPeriodTimeMinGap not support timeKeyFlag:" + timeKeyFlag);
    }

    /**
     * 判断是否相同的时间周期（比较日期是否相同）
     */
    private static boolean isSameTimePeriod(Date date, String day, String pattern) {
        return DateUtil.format(date, pattern).equals(day);
    }

    /**
     * 判断是否有效的小时点
     * @param timeKeyBeginHour - 开始时间（格式 HH）
     * @param timeKeyEndHour - 结束时间（格式 HH）
     * @param hour - 当前小时（格式 HH）
     * @return
     */
    private static boolean isValidHour(String timeKeyBeginHour, String timeKeyEndHour, String hour) {
        if(isOverDay(timeKeyBeginHour, timeKeyEndHour)) {
            boolean flag1 = hour.compareTo(timeKeyBeginHour) >= 0 && hour.compareTo("23") <=0 ;
            boolean flag2 = hour.compareTo("00") >= 0 && hour.compareTo(timeKeyEndHour) <=0 ;
            return flag1 || flag2;
        } else {
            return hour.compareTo(timeKeyBeginHour) >= 0 && hour.compareTo(timeKeyEndHour) <=0;
        }
    }

    /**
     * 判断是否跨天了
     * @param beginHour - 开始时间（格式 HH）
     * @param endHour - 结束时间（格式 HH）
     * @return
     */
    private static boolean isOverDay(String beginHour, String endHour) {
        return beginHour.compareTo(endHour) > 0;
    }

    /**
     * pk结算时，判断是否有效的结算榜单
     * 若是时间分榜的要调整到时间周期的最后1秒
     */
    public static boolean isValidPkSettleRankingTime(RankingConfig ranking, long nowSeconds, int delaySeconds) {
        Date end = ranking.getCalcEndTime();
        Long timeKey = ranking.getTimeKey();
        Date adjustEndTime = notTimeSplit(timeKey) ? end : getCurrTimeValue(false, timeKey, end);
        return nowSeconds < DateUtil.getSeconds(adjustEndTime) + delaySeconds;
    }

    public static Date fromTimeCode(long timeKey, String timeCode) {
        Date date = null;
        if (isSplitByDay(timeKey)) {
            date = DateUtil.getDate(timeCode, DateUtil.PATTERN_TYPE2);
        } else if(isSplitByHour(timeKey)) {
            date = DateUtil.getDate(timeCode, DateUtil.PATTERN_TYPE7);
        } else if(isSplitByWeek(timeKey)) {
            date = DateUtil.getDate(timeCode, DateUtil.PATTERN_TYPE2);
        } else if(isSplitByMonth(timeKey)) {
            date = DateUtil.getDate(timeCode, DateUtil.PATTERN_TYPE2);
        } else if(isSplitBySeason(timeKey)) {
            date = DateUtil.getDate(timeCode, DateUtil.PATTERN_TYPE2);
        } else if(isSplitByYear(timeKey)) {
            date = DateUtil.getDate(timeCode, DateUtil.PATTERN_TYPE2);
        } else if (isSplitBy15Min(timeKey)) {
            date = DateUtil.getDate(timeCode, DateUtil.PATTERN_TYPE8);
        } else if (isSplitBy30Min(timeKey)) {
            date = DateUtil.getDate(timeCode, DateUtil.PATTERN_TYPE8);
        }
        return date;
    }

    public static boolean isSplitByDay(long timeKey) {
        return timeKey == TIME_KEY_BY_DAY;
    }

    public static boolean isSplitByHour(long timeKey) {
        return timeKey == TIME_KEY_BY_HOUR;
    }

    public static boolean isSplitBy15Min(long timeKey) {
        return timeKey == TIME_KEY_BY_15_MIN;
    }

    public static boolean isSplitBy30Min(long timeKey) {
        return timeKey == TIME_KEY_BY_30_MIN;
    }


    public static boolean isSplitByWeek(long timeKey) {
        return timeKey == TIME_KEY_BY_WEEK;
    }

    public static boolean isSplitByMonth(long timeKey) {
        return timeKey == TIME_KEY_BY_MONTH;
    }

    public static boolean isSplitBySeason(long timeKey) {
        return timeKey == TIME_KEY_BY_SEASON;
    }

    public static boolean isSplitByYear(long timeKey) {
        return timeKey == TIME_KEY_BY_YEAR;
    }

    /**
     * 判断非时间分榜标记
     */
    public static boolean notTimeSplit(long timeKey) {
        return !isTimeSplit(timeKey);
    }

    /**
     * 判断是时间分榜标记
     */
    public static boolean isTimeSplit(long timeKey) {
        return timeKey == TIME_KEY_BY_HOUR
                || timeKey == TIME_KEY_BY_DAY
                || timeKey == TIME_KEY_BY_WEEK
                || timeKey == TIME_KEY_BY_MONTH
                || timeKey == TIME_KEY_BY_SEASON
                || timeKey == TIME_KEY_BY_YEAR
                || timeKey == TIME_KEY_BY_15_MIN
                || timeKey == TIME_KEY_BY_30_MIN;
    }

//    private static String to(RankingConfig ranking, String s) {
//        //Date date = findLastBeginTime(ranking, null, toDate(s));
//        Date date = findLastEndTimeForNotify(ranking, null, toDate(s));
//        return DateUtil.format(date);
//    }

    public static Date toDate(String date) {
        return DateUtil.getDate(date);
    }

    /**
     * 检查当前分秒值是否在指定的时间槽中
     * @param solt             - 其中应该刚好有2个元素，且 mm:ss 格式的分秒值
     * @param currMinuteSecond - 当前的分秒值 （格式为：mm:ss)
     */
    public static boolean checkSoltInHour(List<String> solt, String currMinuteSecond) {
        if (solt == null || solt.size() != 2 || StringUtil.isBlank(currMinuteSecond)) {
            return true;
        }
        String begin = solt.get(0);
        String end = solt.get(1);
        return currMinuteSecond.compareTo(begin) >= 0 && currMinuteSecond.compareTo(end) <= 0;
    }

    public static void main(String[] args) {
//        System.out.println(checkSoltInHour(null, ""));
//        System.out.println(checkSoltInHour(Lists.newArrayList(), ""));
//        System.out.println(checkSoltInHour(Lists.newArrayList("1"), ""));
//        System.out.println(checkSoltInHour(Lists.newArrayList("1", "2"), ""));
//        System.out.println(checkSoltInHour(Lists.newArrayList("1", "2", "3"), "4"));
//        System.out.println("\n");
//        System.out.println(checkSoltInHour(Lists.newArrayList("00:00", "59:59"), "00:00"));
//        System.out.println(checkSoltInHour(Lists.newArrayList("00:00", "59:59"), "30:00"));
//        System.out.println(checkSoltInHour(Lists.newArrayList("20:00", "59:59"), "30:00"));
//        System.out.println(checkSoltInHour(Lists.newArrayList("30:00", "59:59"), "30:00"));
//        System.out.println(checkSoltInHour(Lists.newArrayList("30:01", "59:59"), "30:00"));
//        System.out.println("\n");
//        System.out.println(checkSoltInHour(Lists.newArrayList("20:00", "25:00"), "30:00"));
//        System.out.println(checkSoltInHour(Lists.newArrayList("20:00", "30:00"), "30:00"));
//        System.out.println(checkSoltInHour(Lists.newArrayList("20:00", "30:01"), "30:00"));


//        RankingConfig ranking = new RankingConfig();
//        ranking.setTimeKey(2L);
//        ranking.setCalcBeginTime(DateUtil.getDate("2020-08-15 10:00:00"));
//        ranking.setCalcEndTime(DateUtil.getDate("2020-08-20 23:59:59"));
//        ranking.setTimeKeyBegin("20:00:00");
//        ranking.setTimeKeyEnd("03:00:00");
//
////        RankingPhase phase = new RankingPhase();
////        phase.setBeginTime(DateUtil.getDate("2020-08-15 11:40:00"));
////        phase.setEndTime(DateUtil.getDate("2020-08-18 19:51:00"));
//
//        System.out.println(to(ranking, "2020-08-16 00:00:00"));
//        System.out.println(to(ranking, "2020-08-16 01:00:00"));
//        System.out.println(to(ranking, "2020-08-16 02:00:00"));
//        System.out.println(to(ranking, "2020-08-16 03:00:00"));
//        System.out.println(to(ranking, "2020-08-16 04:00:00"));
//        System.out.println(to(ranking, "2020-08-16 05:00:00"));
//        System.out.println(to(ranking, "2020-08-16 06:00:00"));
//        System.out.println(to(ranking, "2020-08-16 07:00:00"));
//        System.out.println(to(ranking, "2020-08-16 08:00:00"));
//        System.out.println(to(ranking, "2020-08-16 09:00:00"));
//        System.out.println(to(ranking, "2020-08-16 10:00:00"));
//        System.out.println(to(ranking, "2020-08-16 11:00:00"));
//        System.out.println(to(ranking, "2020-08-16 12:00:00"));
//        System.out.println(to(ranking, "2020-08-16 13:00:00"));
//        System.out.println(to(ranking, "2020-08-16 14:00:00"));
//        System.out.println(to(ranking, "2020-08-16 15:00:00"));
//        System.out.println(to(ranking, "2020-08-16 16:00:00"));
//        System.out.println(to(ranking, "2020-08-16 17:00:00"));
//        System.out.println(to(ranking, "2020-08-16 18:00:00"));
//        System.out.println(to(ranking, "2020-08-16 19:00:00"));
//        System.out.println(to(ranking, "2020-08-16 20:00:00"));
//        System.out.println(to(ranking, "2020-08-16 21:00:00"));
//        System.out.println(to(ranking, "2020-08-16 22:00:00"));
//        System.out.println(to(ranking, "2020-08-16 23:00:00"));
//        //Date lastEndTime = findLastEndTime(ranking, null, now);
//        //System.out.println("lastBeginTime:" + DateUtil.format(lastBeginTime));
//        //System.out.println("lastEndTime:" + DateUtil.format(lastEndTime));
    }
}
