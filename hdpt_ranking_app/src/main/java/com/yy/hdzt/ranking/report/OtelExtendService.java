package com.yy.hdzt.ranking.report;

import com.yy.hdzt.common.ParameterManager;
import com.yy.hdzt.common.consts.Const;
import com.yy.hdzt.common.utils.DateUtil;
import com.yy.hdzt.common.utils.StringUtil;
import com.yy.hdzt.ranking.event.BaseEvent;
import io.opentelemetry.api.baggage.Baggage;
import io.opentelemetry.api.baggage.BaggageBuilder;
import io.opentelemetry.context.Context;
import io.opentelemetry.context.Scope;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

/**
 * desc: OpenTelemetry 扩展服务，用于设置和管理 otel 上下文信息
 *
 * <AUTHOR>
 * @date 2025-08-05 16:49
 **/
@Service
public class OtelExtendService {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    // 全局开关参数名
    private static final String OTEL_EXTEND_ENABLE_PARAM = "otel_extend_enable";
    // 默认关闭
    private static final String DEFAULT_ENABLE_VALUE = "false";

    /**
     * 检查是否启用 otel 扩展功能
     *
     * @return true 启用，false 禁用
     */
    private boolean isOtelExtendEnabled() {
        try {
            ParameterManager pm = Const.PM;
            if (pm == null) {
                log.warn("ParameterManager instance is null, otel extend disabled");
                return false;
            }
            String enableValue = pm.getParamValue(OTEL_EXTEND_ENABLE_PARAM, DEFAULT_ENABLE_VALUE);
            return "true".equalsIgnoreCase(enableValue) || "1".equals(enableValue);
        } catch (Exception e) {
            log.warn("Failed to get otel extend enable parameter, using default value: {}", DEFAULT_ENABLE_VALUE, e);
            return false;
        }
    }

    /**
     * 为 BaseEvent 设置 otel 上下文信息
     * traceBeginTime 取值是 BaseEvent 的 timestamp
     * scene 取值是 BaseEvent 的实际类名
     *
     * @param event BaseEvent 实例
     * @param runnable 要在设置上下文后执行的操作
     */
    public void setOtelContextForEvent(BaseEvent event, Runnable runnable) {
        if (!isOtelExtendEnabled()) {
            // 开关关闭时直接执行，不设置上下文
            runnable.run();
            return;
        }

        if (event == null) {
            log.warn("BaseEvent is null, skip setting otel context");
            runnable.run();
            return;
        }

        try {
            String traceBeginTime = event.getTimestamp();

            String scene = event.getClass().getSimpleName();

            if (traceBeginTime == null) {
                traceBeginTime = String.valueOf(System.currentTimeMillis());
                log.debug("BaseEvent timestamp is null, using current time: {}", traceBeginTime);
            }

            Context context = OtelExtendTool.setTraceBeginTimeAndScene(traceBeginTime, scene);
            OtelExtendTool.runWithContext(context, runnable);

            log.debug("Set otel context for event: scene={}, traceBeginTime={}", scene, traceBeginTime);
        } catch (Exception e) {
            log.error("Failed to set otel context for event: {}", event.getClass().getSimpleName(), e);
            // 出错时仍然执行原逻辑
            runnable.run();
        }
    }

    /**
     * 为 BaseEvent 设置 otel 上下文信息（无回调版本）
     * 返回设置了上下文的 Context，调用方需要自己管理 Scope
     *
     * @param event BaseEvent 实例
     * @return 设置了上下文的 Context，如果开关关闭或出错则返回当前 Context
     */
    public Context setOtelContextForEvent(BaseEvent event) {
        if (!isOtelExtendEnabled()) {
            return Context.current();
        }

        if (event == null) {
            log.warn("BaseEvent is null, skip setting otel context");
            return Context.current();
        }

        try {
            String traceBeginTime = event.getTimestamp();
            String scene = event.getClass().getSimpleName();

            if (traceBeginTime == null) {
                traceBeginTime = String.valueOf(System.currentTimeMillis());
                log.debug("BaseEvent timestamp is null, using current time: {}", traceBeginTime);
            }

            Context context = OtelExtendTool.setTraceBeginTimeAndScene(traceBeginTime, scene);
            log.debug("Set otel context for event: scene={}, traceBeginTime={}", scene, traceBeginTime);
            return context;
        } catch (Exception e) {
            log.error("Failed to set otel context for event: {}", event.getClass().getSimpleName(), e);
            return Context.current();
        }
    }

    /**
     * OpenTelemetry 扩展工具类，提供底层的 otel 上下文操作方法
     */
    public static class OtelExtendTool {

        // 上下文键常量
        private static final String TRACE_BEGIN_TIME_KEY = "traceBeginTime";
        private static final String SCENE_KEY = "scene";

        /**
         * 设置 trace 开始时间到 otel 上下文中
         *
         * @param traceBeginTime trace 开始时间（毫秒时间戳）
         * @return 返回新的 Context，需要使用 try-with-resources 来管理 Scope
         */
        public static Context setTraceBeginTime(long traceBeginTime) {
            return setTraceBeginTime(String.valueOf(traceBeginTime));
        }

        /**
         * 设置 trace 开始时间到 otel 上下文中
         *
         * @param traceBeginTime trace 开始时间字符串
         * @return 返回新的 Context，需要使用 try-with-resources 来管理 Scope
         */
        public static Context setTraceBeginTime(String traceBeginTime) {
            Baggage currentBaggage = Baggage.fromContext(Context.current());
            Baggage newBaggage = currentBaggage.toBuilder()
                    .put(TRACE_BEGIN_TIME_KEY, traceBeginTime)
                    .build();
            return Context.current().with(newBaggage);
        }

        /**
         * 设置场景信息到 otel 上下文中
         *
         * @param scene 场景信息
         * @return 返回新的 Context，需要使用 try-with-resources 来管理 Scope
         */
        public static Context setScene(String scene) {
            Baggage currentBaggage = Baggage.fromContext(Context.current());
            Baggage newBaggage = currentBaggage.toBuilder()
                    .put(SCENE_KEY, scene)
                    .build();
            return Context.current().with(newBaggage);
        }

        /**
         * 同时设置 trace 开始时间和场景信息到 otel 上下文中
         *
         * @param traceBeginTime trace 开始时间（毫秒时间戳）
         * @param scene 场景信息
         * @return 返回新的 Context，需要使用 try-with-resources 来管理 Scope
         */
        public static Context setTraceBeginTimeAndScene(long traceBeginTime, String scene) {
            return setTraceBeginTimeAndScene(String.valueOf(traceBeginTime), scene);
        }

        /**
         * 同时设置 trace 开始时间和场景信息到 otel 上下文中
         *
         * @param traceBeginTime trace 开始时间字符串
         * @param scene 场景信息
         * @return 返回新的 Context，需要使用 try-with-resources 来管理 Scope
         */
        public static Context setTraceBeginTimeAndScene(String traceBeginTime, String scene) {
            Baggage currentBaggage = Baggage.fromContext(Context.current());
            BaggageBuilder builder = currentBaggage.toBuilder();

            if (traceBeginTime != null) {
                builder.put(TRACE_BEGIN_TIME_KEY, traceBeginTime);
            }
            if (scene != null) {
                builder.put(SCENE_KEY, scene);
            }

            Baggage newBaggage = builder.build();
            return Context.current().with(newBaggage);
        }

        /**
         * 获取当前上下文中的 trace 开始时间
         *
         * @return trace 开始时间字符串，如果不存在则返回 null
         */
        public static String getTraceBeginTime() {
            Baggage baggage = Baggage.fromContext(Context.current());
            return baggage.getEntryValue(TRACE_BEGIN_TIME_KEY);
        }

        /**
         * 获取当前上下文中的 trace 开始时间（长整型）
         *
         * @return trace 开始时间毫秒时间戳，如果不存在或解析失败则返回 -1
         */
        public static long getTraceBeginTimeLong() {
            String timeStr = getTraceBeginTime();
            if (timeStr == null || timeStr.isEmpty()) {
                return -1L;
            }
            try {
                return Long.parseLong(timeStr);
            } catch (NumberFormatException e) {
                return -1L;
            }
        }

        /**
         * 获取当前上下文中的场景信息
         *
         * @return 场景信息字符串，如果不存在则返回 null
         */
        public static String getScene() {
            Baggage baggage = Baggage.fromContext(Context.current());
            return baggage.getEntryValue(SCENE_KEY);
        }

        /**
         * 使用指定的上下文执行操作
         * 这是一个便利方法，用于在特定上下文中执行代码块
         *
         * @param context 要使用的上下文
         * @param runnable 要执行的操作
         */
        public static void runWithContext(Context context, Runnable runnable) {
            try (Scope scope = context.makeCurrent()) {
                runnable.run();
            }
        }

        /**
         * 便利方法：设置 trace 开始时间并执行操作
         *
         * @param traceBeginTime trace 开始时间（毫秒时间戳）
         * @param runnable 要执行的操作
         */
        public static void runWithTraceBeginTime(long traceBeginTime, Runnable runnable) {
            Context context = setTraceBeginTime(traceBeginTime);
            runWithContext(context, runnable);
        }

        /**
         * 便利方法：设置场景信息并执行操作
         *
         * @param scene 场景信息
         * @param runnable 要执行的操作
         */
        public static void runWithScene(String scene, Runnable runnable) {
            Context context = setScene(scene);
            runWithContext(context, runnable);
        }

        /**
         * 便利方法：设置 trace 开始时间和场景信息并执行操作
         *
         * @param traceBeginTime trace 开始时间（毫秒时间戳）
         * @param scene 场景信息
         * @param runnable 要执行的操作
         */
        public static void runWithTraceBeginTimeAndScene(long traceBeginTime, String scene, Runnable runnable) {
            Context context = setTraceBeginTimeAndScene(traceBeginTime, scene);
            runWithContext(context, runnable);
        }
    }
}
