/**
 * RankingHandler.java / 2020年6月8日 下午5:51:24
 * <p>
 * Copyright (c) 2020, YY Inc. All Rights Reserved.
 * <p>
 * 郭立平[<EMAIL>]
 */
package com.yy.hdzt.ranking.handler;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yy.hdzt.common.annotation.Report;
import com.yy.hdzt.common.consts.Const;
import com.yy.hdzt.common.consts.RankDefineType;
import com.yy.hdzt.common.exception.SuperException;
import com.yy.hdzt.common.model.hdzt.HdztActivity;
import com.yy.hdzt.common.model.hdzt.HdztActor;
import com.yy.hdzt.common.model.hdzt.RankingEnrollment;
import com.yy.hdzt.common.support.SysEvHelper;
import com.yy.hdzt.common.thrift.hdzt.ranking.*;
import com.yy.hdzt.common.thrift.hdzt.ranking.HdztRankingService.Iface;
import com.yy.hdzt.common.utils.*;
import com.yy.hdzt.ranking.bean.RankBean;
import com.yy.hdzt.ranking.bean.RankBeanWithPKValue;
import com.yy.hdzt.ranking.bean.RankingConfigVo;
import com.yy.hdzt.ranking.dao.redis.RankingRedisDao;
import com.yy.hdzt.ranking.event.RankDataEvent;
import com.yy.hdzt.ranking.exception.BaseException;
import com.yy.hdzt.ranking.service.*;
import io.opentelemetry.api.GlobalOpenTelemetry;
import io.opentelemetry.api.baggage.Baggage;
import io.opentelemetry.api.trace.Span;
import io.opentelemetry.api.trace.Tracer;
import io.opentelemetry.context.Context;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.rpc.RpcContext;
import org.apache.thrift.TException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.concurrent.atomic.AtomicLong;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020年6月8日 下午5:51:24
 */
public class RankingHandler implements Iface {

    private static final Logger log = LoggerFactory.getLogger(RankingHandler.class);

    private AtomicLong counter = new AtomicLong(0);

    @Autowired
    private DynamicPkService dynamicPkService;

    @Qualifier("rankServiceImpl")
    @Autowired
    private RankService rankService;

    @Autowired
    private RankConfigService rankConfigService;

    @Autowired
    private CacheService cacheService;

    @Autowired
    private RankingPhaseService rankingPhaseService;

    @Autowired
    private HdztActivityService hdztActivityService;

    @Autowired
    private TaskInfoService taskInfoService;

    @Autowired
    private EnrollmentService enrollmentService;

    @Autowired
    private PhasePkgroupService phasePkgroupService;

    @Autowired
    private RankingPhaseQualificationService rankingPhaseQualificationService;

    @Autowired
    private RankingQueryService rankingQueryService;

    @Autowired
    private RankingRedisDao rankingRedisDao;

    @Autowired
    private SettleControlService settleControlService;


    private final Tracer TRACER = GlobalOpenTelemetry.getTracer(RankingHandler.class.getName());

    /*
     * <AUTHOR>
     * @date 2020年6月29日 下午10:06:02
     */
    @Override
    public void ping() throws TException {
    }

    /*
     * <AUTHOR>
     * @date 2020年6月29日 下午10:06:02
     */
    @Override
    public String version() throws TException {
        return "0.0.1";
    }

    @Override
    @Report
    public String currentTime() throws TException {
        // 使用特殊的 0 活动ID来控制默认时间
        return this.getCurrentTime(0L);
    }

    @Override
    @Report
    public String getCurrentTime(long actId) throws TException {
        return DateUtil.format(hdztActivityService.getCurrSysTime(actId));
    }

    @Override
    @Report
    public SimpleResult checkGrey(long actId) throws TException {
        try {
            SimpleResult result = new SimpleResult();
            if (hdztActivityService.isActivityGrey(actId)) {
                result = new SimpleResult(1, "yes, in grey status:" + actId, null);
            } else {
                result = new SimpleResult(2, "no, not in grey status:" + actId, null);
            }
            log.info("checkGrey done@code:{}, reason:{}", result.getCode(), result.getReason());
            return result;
        } catch (Throwable e) {
            log.error("checkGrey exception@actId:{}, err:{}", actId, e.getMessage(), e);
            return new SimpleResult(-99, actId + ":" + e.getMessage(), null);
        }
    }

    /*
     * <AUTHOR>
     * @date 2020年6月29日 下午10:06:02
     */
    @Override
    @Report
    public SimpleResult invoke(long busiId, long type, Map<String, String> data, String sign) throws TException {
        //todo 获取复活的用户 done
        String doSetActHash = "doSetActHash";
        String queryUniqCp = "queryUniqCp";
        String queryUniqPointedCp = "queryUniqPointedCp";
        String queryUniqCpList = "queryUniqCpList";
        String reviveAnchor = "reviveAnchor";
        String queryBatchSrcRanks = "queryBatchSrcRanks";
        if (queryBatchSrcRanks.equals(sign)) {
            return queryBatchSrcRanks(data);
        } else if (reviveAnchor.equalsIgnoreCase(sign)) {
            return reviveAnchor(busiId, type, data);
        } else if (doSetActHash.equals(sign)) {
            SysEvHelper.checkWriteForHistory("invoke", data, true);
            //设置hash
            return hdztActivityService.doSetActHash(busiId, type, data, sign);
        } else if (queryUniqCp.equals(sign)) {
            return hdztActivityService.queryUniqCp(data, false);
        } else if (queryUniqCpList.equals(sign)) {
            return hdztActivityService.queryUniqCpList(data);
        } else if (queryUniqPointedCp.equals(sign)) {
            return hdztActivityService.queryUniqPointedCp(data);
        }

        return hdztActivityService.doSelfDefProcess(busiId, type, data, sign);
        //return new SimpleResult(0, "ok:" + busiId + "," + type, data);
    }

    private SimpleResult queryBatchSrcRanks(Map<String, String> data) {
        SimpleResult result = new SimpleResult();
        result.setCode(0);
        long actId = MapUtils.getLongValue(data, "actId");
        long rankingId = MapUtils.getLongValue(data, "rankingId");
        long phaseId = MapUtils.getLongValue(data, "phaseId");
        long rankingCount = MapUtils.getLongValue(data, "rankingCount");
        String dateStr = MapUtils.getString(data, "dateStr", StringUtils.EMPTY);
        String rankType = MapUtils.getString(data, "rankType");
        String findSrcMembersStr = MapUtils.getString(data, "findSrcMembers");
        List<String> findSrcMembers = null;
        if (StringUtils.startsWith(findSrcMembersStr, StringUtil.OPEN_BRACKET)) {
            findSrcMembers = JSON.parseArray(findSrcMembersStr, String.class);
        }
        String extStr = MapUtils.getString(data, "ext");
        Map<String, String> ext = null;
        if (StringUtils.startsWith(extStr, StringUtil.OPEN_BRACE)) {
            ext = JSON.parseObject(extStr, new TypeReference<HashMap<String, String>>(){});
        }

        Map<String, List<RankBean>> ranks = rankService.queryBatchSrcRanks(actId, rankingId, rankingCount, phaseId, dateStr, findSrcMembers, rankType, ext);
        if (CollectionUtils.isEmpty(ranks)) {
            result.setData(Collections.emptyMap());
            return result;
        }


        Map<String, String> rs = new HashMap<>(ranks.size());
        for (Map.Entry<String, List<RankBean>> entry : ranks.entrySet()) {
            String rankJson = JSON.toJSONString(entry.getValue());
            rs.put(entry.getKey(), rankJson);
        }

        result.setData(rs);

        return result;
    }

    private SimpleResult reviveAnchor(long busiId, long type, Map<String, String> data) {
        String redisKey = data.get("redisKey");
        long group = Convert.toLong(data.get("group"));
        Set<String> smembers = rankingRedisDao.smembers(group, redisKey);
        data.put("data", String.join(",", smembers));
        SimpleResult simpleResult = new SimpleResult();
        simpleResult.setCode(0);
        simpleResult.setData(data);
        return simpleResult;
    }

    /**
     * 检查白名单
     */
    @Override
    @Report
    public SimpleResult checkWhiteList(long actId, RoleType roleType, String roleValue, Map<String, String> extData) throws TException {
        String param = String.format("%s|%s|%s", actId, roleType, roleValue);
        try {
            if (roleType == null || roleType == RoleType.ANY) {
                return new SimpleResult(-1, "invalid:" + param, null);
            }
            if (hdztActivityService.checkWhiteList(actId, roleType.getValue(), roleValue, extData)) {
                return new SimpleResult(0, "yes:" + param, null);
            } else {
                return new SimpleResult(1, "no:" + param, null);
            }
        } catch (Throwable e) {
            log.error("{}) checkWhiteList error, actId:{}, roleType:{}, roleValue:{}, e:{}", actId, roleType, roleValue, e.getMessage(), e);
            return new SimpleResult(-2, "exception:" + param + ", " + e.getMessage(), null);
        }
    }

    /*
     * 更新活动的榜单
     * <AUTHOR>
     * @date 2020年6月29日 下午10:06:02
     */
    @Override
    @Report
    public UpdateRankingResult updateRanking(UpdateRankingRequest request) throws TException {
        Clock clock = new Clock();
        long inx = counter.incrementAndGet();
        try {
            SysEvHelper.checkWriteForHistory("更新榜单", request, true);
            RankDataEvent dataEvent = RankDataEvent.toRankDataEvent(request);
            if (hdztActivityService.adjust(dataEvent)) {
                rankService.updateRanking(dataEvent);
            }
            UpdateRankingResult result = new UpdateRankingResult();
            result.setCode(0);
            result.setReason("ok");
            result.setData(ImmutableMap.of("seq", request.getSeq()));
            log.info("{}) updateRanking done@request:{}, result:{} {}", inx, request, result, clock);
            return result;
        } catch (Throwable e) {
            log.error("{}) updateRanking error,request:{}, e:{} {}", inx, request, e, clock.tag(), e);
            UpdateRankingResult result = new UpdateRankingResult();
            result.setCode(-9999);
            result.setReason(e.getMessage());
            return result;
        }
    }

    /**
     * 打印当前上下文的Span和Baggage信息
     */
    public static void printCurrentContextInfo() {
        // 获取当前上下文
        Context currentContext = Context.current();

        // 获取当前Span信息
        Span currentSpan = Span.fromContext(currentContext);
        log.info("===== 当前Span信息 =====");
        if (currentSpan == Span.getInvalid()) {
            log.info("没有活跃的Span");
        } else {
            log.info("Trace ID: {}", currentSpan.getSpanContext().getTraceId());
            log.info("Span ID: {}", currentSpan.getSpanContext().getSpanId());
            log.info("是否采样: {}", currentSpan.getSpanContext().isSampled());
        }

        // 获取当前Baggage信息
        Baggage currentBaggage = Baggage.fromContext(currentContext);
        log.info("\n===== 当前Baggage信息 =====");
        if (currentBaggage.isEmpty()) {
            log.info("Baggage中没有数据");
        } else {
            currentBaggage.forEach((key, entry) -> {
                log.info("键: {}, 值: {}, 元数据: {}",
                        key,
                        entry.getValue(),
                        entry.getMetadata().getValue());
            });
        }
    }

    @Override
    @Report
    public QuerySingleRankingResult queryRanking(QueryRankingRequest request) throws TException {

        printCurrentContextInfo();


        QuerySingleRankingResult result = new QuerySingleRankingResult();
        if (cacheService.getHdztActivity(request.getActId()) == null) {
            result.setReason("act not exist:" + request.getActId());
            return result;
        }

        try {
            // 获取榜单列表
            long rankingCount = request.getRankingCount();
            long actId = request.getActId();
            long rankingId = request.getRankingId();
            long phaseId = request.getPhaseId();
            String findSrcMember = request.getFindSrcMember();

            String dateStr = request.getDateStr();
            String rankType = request.getRankType();
            Map<String, String> extData = request.getExtData();
            String pointedMember = request.getPointedMember();

            List<RankBean> rankBeans;
            RankBean pointedRankBean = new RankBean();
            if (extData != null && extData.containsKey(Const.RANK_SORT_BY_PKVALUE)) {
                rankBeans = adjustTotalPKValueRank(actId, rankingId, rankingCount, phaseId, dateStr, findSrcMember, rankType, extData, pointedMember, pointedRankBean);
            } else {
                rankBeans = rankingCount > 0 ? rankService.querySingleRank(actId, rankingId, rankingCount, phaseId, dateStr, findSrcMember, rankType, extData) : Lists.newArrayList();
                // 获取指定成员榜单信息
                pointedRankBean = rankService.queryPointedMemberRank(actId, rankingId, phaseId, dateStr, findSrcMember, rankType, pointedMember, extData);
            }
            Rank pointedRank = pointedRankBean == null || StringUtil.isBlank(pointedRankBean.getMember()) ?
                    null : new Rank(pointedRankBean.getMember(), pointedRankBean.getRank(), pointedRankBean.getScore(), "");

            result.setCode(0);
            result.setReason("ok");
            result.setData(toRanks(rankBeans));
            result.setPointedRank(pointedRank);

            return result;
        } catch (Exception e) {
            log.error("queryRanking error,request:{},e:{}", request, e, e);
            result = new QuerySingleRankingResult();
            result.setCode(-999);
            result.setReason(e.getMessage());
            return result;
        }
    }

    /**
     * 调整
     */
    private List<RankBean> adjustTotalPKValueRank(long actId, long rankingId,
                                                  long rankingCount, long phaseId, String dateStr,
                                                  String findSrcMember, String rankType, Map<String, String> extData,
                                                  String pointedMember, RankBean pointedRankBean) {
        //读取主榜,默认1000个
        int count = cacheService.findActivityConfigExt(actId).getTotal_pk_value_count();

        //积分榜
        List<RankBean> mainRank = rankService.querySingleRank(actId, rankingId, count, phaseId, dateStr, findSrcMember, rankType, extData);
        Map<String, RankBean> member2rankBean = mainRank.stream().collect(Collectors.toMap(RankBean::getMember, Function.identity()));
        //pk值榜
        List<RankBean> pkRank = rankService.querySingleRank(actId, rankingId, count, phaseId, dateStr, findSrcMember, RankDefineType.CONTEST_PK, extData);
        Map<String, Integer> member2pkRank = new HashMap<>(1024);
        for (int i = 0; i < pkRank.size(); i++) {
            member2pkRank.put(pkRank.get(i).getMember(), i + 1);
        }

        List<RankBeanWithPKValue> notInclude = new ArrayList<>();
        //封装添加pkvalue 进行排序
        List<RankBeanWithPKValue> rankBeanWithPKValues = new ArrayList<>();
        for (RankBean rankBean : mainRank) {
            RankBeanWithPKValue pkValue = new RankBeanWithPKValue();
            BeanUtils.copyProperties(rankBean, pkValue);
            String member = rankBean.getMember();
            Integer pkValueRank = member2pkRank.get(member);
            if (pkValueRank == null) {
                notInclude.add(pkValue);
            } else {
                pkValue.setPkValueRank(pkValueRank);
                rankBeanWithPKValues.add(pkValue);
            }
        }
        handleNotIncludeMembers(actId, rankingId, phaseId, dateStr, findSrcMember, RankDefineType.CONTEST_PK, extData,
                notInclude, rankBeanWithPKValues);

        Collections.sort(rankBeanWithPKValues);

        //获取需要的数量
        List<RankBean> newRankBeans = rankBeanWithPKValues.subList(0, (int) Math.min(rankingCount, rankBeanWithPKValues.size())).stream().map(it ->
                member2rankBean.get(it.getMember())
        ).collect(Collectors.toList());

        int rank = 1;
        for (RankBean newRankBean : newRankBeans) {
            newRankBean.setRank(rank);
            rank++;
        }

        for (RankBeanWithPKValue bean : rankBeanWithPKValues) {
            if (bean.getMember().equals(pointedMember)) {
                BeanUtils.copyProperties(bean, pointedRankBean);
            }
        }

        return newRankBeans;
    }

    private void handleNotIncludeMembers(long actId, long rankId, long phaseId, String dateStr, String findSrcMember, String rankType, Map<String, String> ext,
                                         List<RankBeanWithPKValue> notInclude, List<RankBeanWithPKValue> rankBeanWithPKValues) {

        if (CollectionUtils.isEmpty(notInclude)) {
            return;
        }
        List<String> members = notInclude.stream().map(RankBeanWithPKValue::getMember).collect(Collectors.toList());
        List<RankBean> rankBeans = rankService.queryPointedMemberRank(actId, rankId, phaseId, dateStr, findSrcMember, rankType, members, ext);
        Map<String, RankBean> map = rankBeans.stream().filter(rankBean -> rankBean.getRank() != -1).collect(Collectors.toMap(RankBean::getMember, Function.identity()));
        for (RankBeanWithPKValue pkValue : notInclude) {
            RankBean rankBean = map.get(pkValue.getMember());
            if (rankBean != null) {
                pkValue.setPkValueRank(rankBean.getRank());
                rankBeanWithPKValues.add(pkValue);
            }
        }
    }

    @Override
    @Report
    public QueryMulRankingResult queryTimeRank(QueryTimeRankingRequest request) throws TException {
        long actId = request.getActId();
        long defaultCount = request.getRankingCount();
        Map<String, List<RankBean>> queryResult = rankService.queryHourRank(actId, request.getRankingId(), defaultCount, null);
        Map<String, List<Rank>> data = toRankMap(queryResult);
        Map<String, Map<String, List<Rank>>> rankIdMap = Maps.newHashMap();
        rankIdMap.put(request.getRankingId() + "", data);
        QueryMulRankingResult result = new QueryMulRankingResult();
        result.setCode(0);
        result.setReason("ok");
        result.setData(rankIdMap);
        return result;
    }


    /**
     * 查询榜单配置
     */
    @Override
    @Report
    public RankingConfigResponse queryRankingConfig(RankingConfigRequest request) throws TException {
        try {
            Date currentTime = request.getCurrentDate() == 0 ? new Date() : new Date(request.getCurrentDate());
            Map<Long, RankingConfigVo> rankingConfigVoMap = rankConfigService.getRankConfig(request.getActId(), request.getRankingIds(), request.getCurPhaseId(), currentTime, request.getExtData());
            Map<Long, HdztActor> hdztActorMap = cacheService.getHdztActorMap();
            Map<Long, RankingInfo> rankingInfoMap = RankingConfigVo.toRankingInfoMap(rankingConfigVoMap, hdztActorMap);

            RankingConfigResponse rankingConfigResponse = new RankingConfigResponse();
            rankingConfigResponse.setCode(0);
            rankingConfigResponse.setReason("ok");
            rankingConfigResponse.setData(rankingInfoMap);
            return rankingConfigResponse;
        } catch (Exception e) {
            log.error("queryRankingConfig error,request:{},e:{}", request, e, e);
            RankingConfigResponse rankingConfigResponse = new RankingConfigResponse();
            rankingConfigResponse.setCode(-999);
            rankingConfigResponse.setReason(e.getMessage());
            return rankingConfigResponse;
        }
    }

    /**
     * 查询参赛者角色信息以及分数信息
     */
    @Override
    @Report
    public ActorRankingInfoResponse queryActorRankingInfo(ActorRankingInfoRequest request) throws TException {
        try {
            List<ActorInfoItem> list = rankConfigService.queryActorRankingInfo(request);
            ActorRankingInfoResponse result = new ActorRankingInfoResponse();
            result.setActorStatus(list);
            result.setCode(0);
            result.setReason("ok");
            return result;
        } catch (Exception e) {
            log.error("queryActorRankingInfo error,request:{},e:{}", request, e, e);
            ActorRankingInfoResponse response = new ActorRankingInfoResponse();
            response.setCode(-999);
            response.setReason(e.getMessage());
            return response;
        }
    }

    /**
     * 查询活动配置信息
     *
     * @param request
     * @return
     * @throws TException
     */
    @Override
    @Report
    public ActivityInfoResult queryActInfo(ActivityInfoRequest request) throws TException {

        try {
            Long actId = request.getActId();
            ActivityInfo activityInfo = new ActivityInfo();

            HdztActivity hdztActivity = cacheService.getHdztActivity(actId);
            if (hdztActivity != null) {
                activityInfo = toActivityInfo(hdztActivity);
            }

            ActivityInfoResult result = new ActivityInfoResult();
            result.setCode(0);
            result.setReason("ok");
            result.setData(activityInfo);
            return result;

        } catch (Exception e) {
            log.error("queryActInfo error,request:{},e:{}", request, e, e);
            ActivityInfoResult rankingConfigResponse = new ActivityInfoResult();
            rankingConfigResponse.setCode(-999);
            rankingConfigResponse.setReason(e.getMessage());
            return rankingConfigResponse;
        }
    }

    /**
     * 查询活动配置扩展信息
     */
    @Override
    @Report
    public ActivityAttrResult queryActAttr(ActivityInfoRequest request) throws TException {
        ActivityAttrResult result = new ActivityAttrResult();
        try {
            Map<String, String> attr = cacheService.findActivityConfigExt(request.getActId()).toRawMap();
            result.setData(attr);
            result.setCode(0);
        } catch (IllegalAccessException e) {
            result.setCode(-1);
            result.setReason(e.getMessage());
            log.error("queryActAttr exception@request:{}, err:{}", request, e.getMessage(), e);
        }
        return result;
    }

    private ActivityInfo toActivityInfo(HdztActivity hdztActivity) {
        ActivityInfo activityInfo = new ActivityInfo();
        activityInfo.setActId(hdztActivity.getActId());
        activityInfo.setBusiId(hdztActivity.getBusiId());
        activityInfo.setActName(hdztActivity.getActName());
        activityInfo.setStatus(hdztActivity.getStatus());
        activityInfo.setActBgUrl(hdztActivity.getActBgUrl());
        activityInfo.setDetailUrl(hdztActivity.getDetailUrl());
        activityInfo.setBeginTime(hdztActivity.getBeginTime().getTime());
        activityInfo.setEndTime(hdztActivity.getEndTime().getTime());
        activityInfo.setActType(hdztActivity.getActType());
        activityInfo.setBeginTimeShow(hdztActivity.getBeginTimeShow().getTime());
        activityInfo.setEndTimeShow(hdztActivity.getEndTimeShow().getTime());
        return activityInfo;
    }

    @Override
    @Report
    public ActivityInfosResult queryActInfos(ActivityInfosRequest request) throws TException {
        ActivityInfosResult result = new ActivityInfosResult();
        try {
            Map<Long, HdztActivity> activityMap = cacheService.getHdztActivityMap();

            List<ActivityInfo> activityInfos = Lists.newArrayList();
            for (Long actId : activityMap.keySet()) {
                HdztActivity hdztActivity = activityMap.get(actId);
                activityInfos.add(toActivityInfo(hdztActivity));
            }

            result.setData(activityInfos);
            result.setCode(0);
            result.setReason("ok");
            return result;

        } catch (Exception e) {
            log.error("queryActInfos error,request:{},e:{}", request, e, e);

            result.setCode(-999);
            result.setReason(e.getMessage());
            return result;
        }
    }

    // 待删除中。。。。 added by guoliping / 2022-06-22
    @Deprecated
    @Override
    @Report
    public ActivityInfosResult queryZwActInfos(ActivityInfosRequest request) throws TException {
        ActivityInfosResult result = new ActivityInfosResult();
        try {
            result.setCode(-1);
            result.setReason("待删除中");
            return result;
        } catch (Exception e) {
            log.error("queryActInfos error,request:{},e:{}", request, e, e);
            result.setCode(-999);
            result.setReason("待删除中:" + e.getMessage());
            return result;
        }
    }


    //查询阶段配置
    @Override
    @Report
    public QueryRankingPhaseResponse queryRankingPhase(QueryRankingPhaseRequest request) throws TException {
        try {
            QueryRankingPhaseResponse result = new QueryRankingPhaseResponse();
            result.setData(rankingPhaseService.queryRankingPhase(request));
            result.setCode(0);
            result.setReason("ok");
            return result;
        } catch (Exception e) {
            log.error("queryRankingPhase error,request:{},e:{}", request, e, e);
            QueryRankingPhaseResponse response = new QueryRankingPhaseResponse();
            response.setCode(-999);
            response.setReason(e.getMessage());
            return response;
        }
    }

    @Override
    @Report
    public QueryRankIdResponse queryRankId(QueryRankIdRequest request) throws TException {
        try {
            Map<String, List<Long>> rankIdMap = null;
            if (request.getQueryType() == 0) {
                rankIdMap = rankConfigService.queryRankId(request);
            } else if (request.getQueryType() == 1) {
                rankIdMap = rankingQueryService.queryNotInRaceRankId(request);
            } else {
                log.error("queryRankId,not support query type,request:{}", JSON.toJSONString(request));
            }
            QueryRankIdResponse result = new QueryRankIdResponse();
            result.setRankIdMap(rankIdMap);
            result.setCode(0);
            result.setReason("ok");
            return result;
        } catch (Exception e) {
            log.error("queryRankId error,request:{},e:{}", request, e, e);
            QueryRankIdResponse response = new QueryRankIdResponse();
            response.setCode(-999);
            response.setReason(e.getMessage());
            return response;
        }
    }

    @Override
    @Report
    public QueryRankPhasePairResponse queryRankPhasePair(QueryRankIdRequest request) throws TException {
        try {
            Map<String, List<RankPhasePair>> rankIdMap = rankConfigService.queryRankPhasePair(request);
            QueryRankPhasePairResponse result = new QueryRankPhasePairResponse();
            result.setRankIdMap(rankIdMap);
            result.setCode(0);
            result.setReason("ok");
            return result;
        } catch (Exception e) {
            log.error("queryRankPhasePair error,request:{},e:{}", request, e, e);
            QueryRankPhasePairResponse response = new QueryRankPhasePairResponse();
            response.setCode(-999);
            response.setReason(e.getMessage());
            return response;
        }
    }

    @Override
    @Report
    public QueryEnrollmentInfoResponse queryEnrollment(QueryEnrollmentInfoRequest request) throws TException {
        Clock clock = new Clock();
        QueryEnrollmentInfoResponse response = new QueryEnrollmentInfoResponse();
        try {

            List<EnrollmentInfo> items = null;
            if (request.isNocache()) {
                items = enrollmentService.queryEnrollmentInfoNoCache(request.getActId(), request.getRoleType(), request.getMemberIds());

            } else {
                items = enrollmentService.queryEnrollmentInfo(
                        request.getActId(),
                        request.getPageSize(), request.getPage(),
                        request.getMemberIds());
            }
            response.setItems(items);
            response.setCode(0);
            response.setReason("ok");
            return response;
        } catch (Exception e) {
            log.error("queryEnrollment error,request:{}, e:{} {}", request, e.getMessage(), e, clock.tag());
            response.setCode(-999);
            response.setReason(e.getMessage());
            return response;
        }
    }

    @Override
    @Report
    public SaveEnrollmentInfoResponse saveEnrollment(SaveEnrollmentInfoRequest request) throws TException {

        Clock clock = new Clock();
        long actId = request.getActId();
        SaveEnrollmentInfoResponse response = new SaveEnrollmentInfoResponse();
        EnrollmentInfo enrollmentInfo = request.getEnrollmentInfo();
        String remark = Optional.ofNullable(request.getRemark()).orElse("");
        String seq = Optional.ofNullable(request.getSeq()).orElse("");
        try {
            Assert.notNull(enrollmentInfo, "enrollmentInfo is null");

            boolean isUpdate = request.isUpdate();
            RankingEnrollment rankingEnrollment = new RankingEnrollment();
            BeanUtils.copyProperties(enrollmentInfo, rankingEnrollment);
            //请求ip和接收ip
            String fromIp = RpcContext.getContext().getRemoteAddressString();
            String acceptIp = SystemUtil.getWorkerBrief();

            remark = StringUtils.isBlank(remark) ? "" : remark + ",";
            remark += "thrift enroll#" + DateUtil.today() + ", fromIp:" + fromIp + ",acceptIp:" + acceptIp + ",seq:" + seq;
            rankingEnrollment.setRemark(remark);
            String extJson = enrollmentInfo.getExtData() == null ? "" :
                    enrollmentInfo.getExtData().getOrDefault("extData", "");
            rankingEnrollment.setExtjson(extJson);

            int addOrUpdateResult = enrollmentService.saveEnroll(actId, rankingEnrollment, seq, isUpdate);

            if (addOrUpdateResult == 0) {
                response.setReason("报名成功。");
            } else {
                response.setCode(addOrUpdateResult);
                response.setReason(addOrUpdateResult == 1 ? "重复报名" : "操作频繁");
            }
            return response;
        } catch (Exception e) {
            log.error("queryEnrollment error,request:{}, e:{} {}", request, e.getMessage(), e, clock.tag());
            response.setCode(-999);
            response.setReason(e.getMessage());
            return response;
        }
    }

    //查询用户任务完成情况
    @Override
    @Report
    public QueryUserTaskResponse queryUserTaskInfo(QueryUserTaskRequest request) throws TException {
        try {
            QueryUserTaskResponse result = taskInfoService.queryUserTask(request);
            result.setCode(0);
            result.setReason("ok");
            return result;
        } catch (Exception e) {
            log.error("queryUserTaskInfo error,request:{},e:{}", request, e, e);
            QueryUserTaskResponse response = new QueryUserTaskResponse();
            response.setCode(-999);
            response.setReason(e.getMessage());
            return response;
        }
    }

    /**
     * 查询批量榜单信息
     */
    @Override
    @Report
    public QueryBatchRankingResult queryBatchRanking(QueryBatchRankingRequest request) throws TException {
        try {
            Map<String, BatchRankingItem> rankMap = Maps.newHashMap();
            for (Map.Entry<String, QueryRankingRequest> entry : request.getBatchQuestData().entrySet()) {
                QuerySingleRankingResult singleRankingResult = queryRanking(entry.getValue());
                BatchRankingItem batchRankingItem = new BatchRankingItem(singleRankingResult.getData(), singleRankingResult.getPointedRank(), Maps.newHashMap());
                rankMap.put(entry.getKey(), batchRankingItem);
            }
            QueryBatchRankingResult result = new QueryBatchRankingResult();
            result.setCode(0);
            result.setReason("ok");
            result.setItems(rankMap);

            return result;
        } catch (Exception e) {
            log.error("queryBatchRanking error,request:{},e:{}", request, e, e);
            QueryBatchRankingResult result = new QueryBatchRankingResult();
            result.setCode(-999);
            result.setReason(e.getMessage());
            return result;
        }
    }

    /**
     * 查询pk信息-可指定返回积分和排名
     *
     * @param request
     * @return
     * @throws TException
     */
    @Override
    @Report
    public QueryPkInfoResponse queryPkInfo(QueryPkInfoRequest request) throws TException {
        try {
            PkInfo pkConfig = phasePkgroupService.getPkInfo(request.getActId(), request.getSrcRankId(), request.getPhaseId(), request.getDateStr(), request.getReturnData(), request.getQueryDateStr());
            QueryPkInfoResponse result = new QueryPkInfoResponse();
            if (pkConfig != null) {
                result.setCode(0);
                result.setReason("ok");
                result.setPkConfig(pkConfig);
            } else {
                result.setCode(-999);
                result.setReason("can not find RankingPhasePk.");
            }

            return result;
        } catch (BaseException bE) {
            QueryPkInfoResponse response = new QueryPkInfoResponse();
            response.setCode(bE.getCode());
            response.setReason(bE.getMessage());
            return response;
        } catch (Exception e) {
            log.error("QueryPkInfo error,request:{},e:{}", request, e, e);
            QueryPkInfoResponse response = new QueryPkInfoResponse();
            response.setCode(-999);
            response.setReason(e.getMessage());
            return response;
        }
    }

    /**
     * 查询晋级分组信息-可指定返回积分和排名
     *
     * @param request
     * @return
     * @throws TException
     */
    @Override
    @Report
    public QueryQualificationGroupResponse queryQualificationGroup(QueryQualificationGroupRequest request) throws TException {
        try {
            MemberGroup queryQualificationGroup = rankingPhaseQualificationService.queryQualificationGroup(request.getActId(), request.getRankId(), request.getPhaseId(), request.getReturnData(), request.getQueryDateStr());
            QueryQualificationGroupResponse result = new QueryQualificationGroupResponse();
            result.setCode(0);
            result.setReason("ok");
            result.setMemberGroup(queryQualificationGroup);
            return result;

        } catch (BaseException bE) {
            QueryQualificationGroupResponse response = new QueryQualificationGroupResponse();
            response.setCode(bE.getCode());
            response.setReason(bE.getMessage());
            return response;
        } catch (Exception e) {
            log.error("queryQualificationGroup error,request:{},e:{}", request, e, e);
            QueryQualificationGroupResponse response = new QueryQualificationGroupResponse();
            response.setCode(-999);
            response.setReason(e.getMessage());
            return response;
        }
    }

    @Override
    @Report
    public QueryHdztActorInfoResponse queryHdztActorInfo(QueryHdztActorInfoRequest request) throws TException {
        try {
            Map<Long, HdztActor> hdztActors = cacheService.getHdztActorMap();
            Map<Long, HdztActorInfo> roles = Maps.newLinkedHashMap();
            for (Long key : hdztActors.keySet()) {
                HdztActor hdztActor = hdztActors.get(key);
                HdztActorInfo hdztActorInfo = new HdztActorInfo();
                hdztActorInfo.setBusiId(hdztActor.getBusiId());
                hdztActorInfo.setRole(hdztActor.getRole());
                hdztActorInfo.setName(hdztActor.getName());
                hdztActorInfo.setType(hdztActor.getType());
                hdztActorInfo.setRemark(hdztActor.getRemark());
                hdztActorInfo.setExtjson(hdztActor.getExtjson());
                hdztActorInfo.setCtime(hdztActor.getCtime().getTime());
                hdztActorInfo.setUtime(hdztActor.getUtime().getTime());

                roles.put(key, hdztActorInfo);
            }
            QueryHdztActorInfoResponse result = new QueryHdztActorInfoResponse();
            result.setRoles(roles);
            result.setCode(0);
            result.setReason("ok");
            return result;
        } catch (Exception e) {
            log.error("queryHdztActorInfo error,request:{},e:{}", request, e, e);
            QueryHdztActorInfoResponse response = new QueryHdztActorInfoResponse();
            response.setCode(-999);
            response.setReason(e.getMessage());
            return response;
        }
    }

    @Override
    @Report
    public QueryZsetRawDataResponse queryZsetRawData(QueryZsetRawDataRequest request) throws TException {
        Clock clock = new Clock();
        QueryZsetRawDataResponse response = new QueryZsetRawDataResponse();
        try {
            List<List<ZsetMember>> rawDatas = rankingQueryService.queryZsetRawData(request);
            response.setRawDatas(rawDatas);
            response.setCode(0);
            response.setReason("ok");
        } catch (Exception e) {
            log.error("queryZsetRawData exception@request:{}, err:{} {}", request, e.getMessage(), clock.tag(), e);
            response.setCode(SuperException.getErrCode(e));
            response.setReason(e.getMessage());
        }
        return response;
    }

    @Override
    @Report
    public RankingTaskItemResponse queryRankingTaskItem(RankingTaskItemRequest request) throws TException {
        Clock clock = new Clock();
        RankingTaskItemResponse response = null;
        try {

            response = taskInfoService.queryRankingTaskItem(request.getActId(), request.getRankId(), request.getPhaseId());

        } catch (Exception e) {
            log.error("queryRankingTaskItem exception@request:{}, err:{} {}", request, e.getMessage(), clock.tag(), e);

            response = new RankingTaskItemResponse();
            response.setCode(SuperException.getErrCode(e));
            response.setReason(e.getMessage());
        }
        return response;
    }

    @Override
    @Report
    public SimpleResult setDynamicPkMembers(long actId, long rankId, long phaseId, String pkMembers, long opUid, String seq) {
        return dynamicPkService.setDynamicPkMembers(actId, rankId, phaseId, pkMembers, opUid, seq);
    }


    @Override
    @Report
    public SimpleResult queryDynamicPkMembersPreview(long actId, long rankId, long phaseId, long opUid) {
        return dynamicPkService.getPkMembersPreview(actId, rankId, phaseId, opUid);
    }

    @Override
    @Report
    public NotifySettleResponse notifySettle(NotifySettleRequest request) throws TException {
        Clock clock = new Clock();
        long actId = request.getActId();
        Date now = hdztActivityService.getCurrSysTime(request.getActId());
        NotifySettleResponse response = null;
        try {
            SysEvHelper.checkWriteForHistory("通知结算", request, true);
            response = settleControlService.notifySettle(request, now);
            log.info("notifySettle done@request:{}, response:{} {}", request, response, clock.tag());
        } catch (Exception e) {
            response = new NotifySettleResponse();
            response.setCode(SuperException.getErrCode(e));
            response.setReason(e.getMessage());
            response.setTime(DateUtil.format(now));
            log.error("notifySettle exception@request:{}, response:{} {}", request, response, clock.tag(), e);
        }
        return response;
    }

    @Override
    @Report
    public MemberRankingInfoResponse queryMemberRankingInfo(MemberRankingInfoRequest request) throws TException {
        try {
            Map<String, MemberRankingInfo> map = rankingQueryService.queryActorRankingInfo(request);
            MemberRankingInfoResponse result = new MemberRankingInfoResponse();
            result.setMemberRankingInfos(map);
            result.setCode(0);
            result.setReason("ok");
            return result;
        } catch (Exception e) {
            log.error("queryMemberRankingInfo error,request:{},e:{}", request, e, e);
            MemberRankingInfoResponse response = new MemberRankingInfoResponse();
            response.setCode(SuperException.getErrCode(e));
            response.setReason(SuperException.getErrMessage(e));
            return response;
        }
    }

    @Override
    @Report
    public QueryRankingByScoreResponse queryRankingByScore(QueryRankingByScoreRequest request) throws TException {
        try {
            List<RankingByScoreInfo> list = rankingQueryService.queryRankingByScore(request);
            QueryRankingByScoreResponse result = new QueryRankingByScoreResponse();
            result.setRankingByScoreInfos(list);
            result.setCode(0);
            result.setReason("ok");
            return result;
        } catch (Exception e) {
            log.error("queryRankingByScore error,request:{},e:{}", request, e, e);
            QueryRankingByScoreResponse response = new QueryRankingByScoreResponse();
            response.setCode(SuperException.getErrCode(e));
            response.setReason(SuperException.getErrMessage(e));
            return response;
        }
    }


    /**
     * 数据转换
     *
     * <AUTHOR>
     * @date 2020年6月30日 下午9:49:56
     */
    private Map<Long, Map<String, List<Rank>>> toRanks(Map<Long, Map<String, List<RankBean>>> queryResult) {
        Map<Long, Map<String, List<Rank>>> data = Maps.newLinkedHashMap();
        for (Long rankId : queryResult.keySet()) {
            Map<String, List<Rank>> map = data.get(rankId);
            if (map == null) {
                map = Maps.newLinkedHashMap();
                data.put(rankId, map);
            }

            Map<String, List<RankBean>> mapList = queryResult.get(rankId);
            for (String key : mapList.keySet()) {
                List<Rank> list = map.get(key);
                if (list == null) {
                    list = Lists.newArrayList();
                    map.put(key, list);
                }

                for (RankBean bean : mapList.get(key)) {
                    Rank rank = toRank(bean);
                    list.add(rank);
                }
            }
        }
        return data;
    }


    private Map<String, List<Rank>> toRankMap(Map<String, List<RankBean>> queryResult) {
        Map<String, List<Rank>> data = Maps.newLinkedHashMap();
        for (String key : queryResult.keySet()) {
            List<Rank> rankItems = toRanks(queryResult.get(key));
            data.put(key, rankItems);
        }
        return data;
    }

    protected List<Rank> toRanks(List<RankBean> rankBeans) {
        List<Rank> list = Lists.newArrayList();
        if (CollectionUtils.isEmpty(rankBeans)) {
            return list;
        }
        for (RankBean bean : rankBeans) {
            Rank rank = toRank(bean);
            list.add(rank);
        }
        return list;
    }

    private Rank toRank(RankBean bean) {
        Rank rank = new Rank();
        rank.setMember(bean.getMember());
        rank.setRank(bean.getRank());
        rank.setScore(bean.getScore());
        rank.setItemDesc(bean.getItemDesc());
        return rank;

    }
}
