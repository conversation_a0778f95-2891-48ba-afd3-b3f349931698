/**
 * HdztActInfoService.java / 2020年4月8日 下午5:24:13
 * <p>
 * Copyright (c) 2020, YY Inc. All Rights Reserved.
 * <p>
 * 郭立平[<EMAIL>]
 */
package com.yy.hdzt.ranking.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.yy.hdzt.common.client.WebdbThriftClient;
import com.yy.hdzt.common.consts.ActStatus;
import com.yy.hdzt.common.consts.Const;
import com.yy.hdzt.common.consts.ParamName;
import com.yy.hdzt.common.exception.SuperException;
import com.yy.hdzt.common.model.hdzt.*;
import com.yy.hdzt.common.notify.BaiduInfoFlowRobotService;
import com.yy.hdzt.common.support.SysEvHelper;
import com.yy.hdzt.common.thrift.hdzt.ranking.EnrollmentInfo;
import com.yy.hdzt.common.thrift.hdzt.ranking.RoleType;
import com.yy.hdzt.common.thrift.hdzt.ranking.SimpleResult;
import com.yy.hdzt.common.utils.*;
import com.yy.hdzt.ranking.bean.*;
import com.yy.hdzt.ranking.commons.HdztConst;
import com.yy.hdzt.ranking.dao.mysql.RankingMysqlDao;
import com.yy.hdzt.ranking.dao.redis.RankingRedisDao;
import com.yy.hdzt.ranking.event.RankDataEvent;
import com.yy.hdzt.ranking.exception.RankingException;
import com.yy.hdzt.ranking.support.TimeKeyHelper;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020年4月8日 下午5:24:13
 */
@Service
public class HdztActivityService {

    private Logger log = LoggerFactory.getLogger(HdztActivityService.class);

    @Autowired
    private CacheService cacheService;

    @Autowired
    private KeyService keyService;

    @Autowired
    private RankingRedisDao rankingRedisDao;

    @Autowired
    private EnrollmentService enrollmentService;

    @Autowired
    private WebdbThriftClient webdbThriftClient;

    @Autowired
    private RedisGroupService redisGroupService;

    @Autowired
    private BaiduInfoFlowRobotService baiduInfoFlowRobotService;

    @Autowired
    private RankingMysqlDao rankingMysqlDao;

    /**
     * 静态的角色白名单
     */
    private final Map<Long, Set<String>> roleWhiteListMap = Maps.newConcurrentMap();

    public static final String VIRT_TIME_KEY = HdztConst.LOCAL_DEBUG_FLAG ? "act_%s_virt_time_local" : "act_%s_virt_time";


    /**
     * 当前环境是否是灰度环境
     *
     * @return
     */
    public boolean isActivityGrey(long actId) {
        return cacheService.findActivityConfigExt(actId).getActivity_grey_status() == 1;
    }

    public Date getCurrSysTime(long actId) {
        Date now = new Date();
        if (!SysEvHelper.isDeploy() || isActivityGrey(actId)) {
            Date virDate = this.getVirDate(actId);
            if (virDate != null) {
                now = virDate;
            }
        }
        return now;
    }


    //返回 null 代表没设置虚拟时间
    private Date getVirDate(long actId) {
        HdztActivity hdztActivity = cacheService.getHdztActivity(actId);
        long group = hdztActivity == null ? HdztConst.REDIS_BASE_GROUP : redisGroupService.getGroupCode(actId, RedisGroupService.RANK_ID_NON);
        String time = rankingRedisDao.get(group, String.format(VIRT_TIME_KEY, actId));
        if (!StringUtil.isBlank(time)) {
            return DateUtil.getDate(time);
        }
        return null;
    }


    /**
     * 调整上报数据：1）项目转换， 2）时延调整，3）角色调整
     */
    public boolean adjust(RankDataEvent dataEvent) {
        //seq 不可为空(刚对接时可能会发生这种情况）
        long actId = dataEvent.getActId();
        long timestamp = dataEvent.getTimestamp();
        if (StringUtil.isBlank(dataEvent.getSeq())) {
            log.warn("adjust skip1@seq is blank! actId:{}, timestamp:{}", actId, timestamp);
            return false;
        }

        // 若灰度状态检查失败，直接返回, 防止后面写操作残留数据（这里是在角色转换前做的检查，灰度测试时可能存在误配，但不会影响生产）
        if (!this.checkUpdateRanking(dataEvent)) {
            return false;
        }

        // 重要检查！！！！ 生产不是灰度状态时检查活动是否开始，防止活动还没开始后续写操作残留数据
        if (SysEvHelper.isDeploy() && !isActivityGrey(actId)) {
            Date reqTime = new Date(timestamp);
            if (this.getHdztActivity(actId, reqTime) == null) {
                log.info("adjust skip2@activity invalid now! actId:{}, seq:{}, reqTime:{}", actId, dataEvent.getSeq(), DateUtil.format(reqTime));
                return false;
            }
        }

        // 项目转换
        if (!this.updateItemId(dataEvent)) {
            return false;
        }

        // 针对请求到达延时，做请求时间调整（安全结算需要）
        this.adjustRankDataEventTime(dataEvent);

        // 转换角色分组
        this.adjustRankDataEventActors(dataEvent);

        return true;
    }

    /**
     * 将业务的项目标识统一转成活动中台认可的标识
     *
     * <AUTHOR>
     * @date 2020年7月24日 下午8:09:19
     */
    public boolean updateItemId(RankDataEvent dataEvent) {
        long actId = dataEvent.getActId();
        String itemId = dataEvent.getItemId();
        long busiId = dataEvent.getBusiId();
        String hdztItemId = cacheService.getRankingItemTransform(actId, busiId, itemId);
        if (StringUtil.isBlank(hdztItemId)) {
            //开启#占位符
            if (cacheService.findActivityConfigExt(actId).getOpen_item_placeholder() == 1) {
                hdztItemId = cacheService.getRankingItemTransform(actId, busiId, HdztConst.DEFAULT_RANKING_ITEM_TRANSFORM);
            }
            if (StringUtil.isBlank(hdztItemId)) {
                log.error("updateItemId fail@can't change {} {} {} to hdzt item, seq:{}", actId, busiId, itemId, dataEvent.getSeq());
                return false;
            }
        }
        dataEvent.setItemId(hdztItemId);
        return true;
    }

    /**
     * 若请求时间比系统当前时间延迟超过 RANKING_UPDATE_REQ_DELAY 毫秒，
     * 则将请求时间调整为当前时间（结合延迟 RANKING_DELAY_SETTLE 毫秒结算使用）
     *
     * <AUTHOR>
     * @date 2020年8月4日 上午9:21:38
     */
    public void adjustRankDataEventTime(RankDataEvent dataEvent) {
        if (HdztConst.LOCAL_DEBUG_FLAG) {
            return;
        }
        if (dataEvent.getExtData() != null && dataEvent.getExtData().containsKey(HdztConst.RANK_MOCK)) {
            return;
        }
        long actId = dataEvent.getActId();
        if (!dataEvent.notAdjustEventTimeDelay()) {
            Date currSysTime = this.getCurrSysTime(actId);
            long diff = currSysTime.getTime() - dataEvent.getTimestamp();
            if (diff > Const.RANKING_UPDATE_REQ_DELAY) {
                dataEvent.setTimestamp(currSysTime.getTime());
                log.warn("adjustRankDataEventTime done@delay {} > {} milliseconds, seq:{}, busiId:{}, actId:{}", diff, Const.RANKING_UPDATE_REQ_DELAY,
                        dataEvent.getSeq(), dataEvent.getBusiId(), dataEvent.getActId());
            }

            //虚拟时间
            if (!SysEvHelper.isDeploy() || isActivityGrey(actId)) {
                Date virDate = this.getVirDate(dataEvent.getActId());
                if (virDate != null) {
                    dataEvent.setTimestamp(virDate.getTime());
                }
            }
        }
    }


    /**
     * 判断给定的项目是否是计榜项目
     */
    public boolean isRankItem(RankingConfig ranking, String itemId) {
        // 榜单不限制计榜项目时，则所有项目都可用来计榜
        if (ranking.getLimitItem() != HdztConst.LIMIT_ITEM_YES) {
            return true;
        }

        if (StringUtil.isBlank(itemId)) {
            return false;
        }

        itemId = StringUtil.trim(itemId);
        RankingItem rankingItem = cacheService.getRankingItem(ranking.getActId(), ranking.getRankId());
        if (rankingItem == null) {
            return false;
        }


        // 别名不空时，若和别名相同，也认为是计榜项目
        String itemAlias = StringUtil.trim(rankingItem.getItemAlias());
        if (!itemAlias.isEmpty() && itemId.equals(itemAlias)) {
            return true;
        }

        String itemIds = String.valueOf(rankingItem.getItemIds());
        String[] ary = itemIds.split(",");
        if (ary != null && ary.length > 0) {
            for (String elm : ary) {
                if (itemId.equals(elm.trim())) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 获取项目别名，没有时返回项目原名
     */
    public String getItemAlias(long actId, long rankId, String itemId) {
        itemId = StringUtil.trim(itemId);
        if (itemId.isEmpty()) {
            return null;
        }

        RankingItem rankingItem = cacheService.getRankingItem(actId, rankId);
        if (rankingItem == null) {
            return null;
        }

        String itemAlias = StringUtil.trim(rankingItem.getItemAlias());
        String itemIds = String.valueOf(rankingItem.getItemIds());
        String[] ary = itemIds.split(",");
        if (ary != null && ary.length > 0) {
            for (String elm : ary) {
                if (itemId.equals(elm.trim())) {
                    return itemAlias.isEmpty() ? itemId : itemAlias;
                }
            }
        }

        return null;
    }

    /**
     * 判断给定的任务项目是否全在计榜项目中
     */
    public boolean isAllInRankItem(RankingConfig ranking, List<RankingTaskItem> taskItems) {
        if (CollectionUtils.isEmpty(taskItems)) {
            return false;
        }
        for (RankingTaskItem item : taskItems) {
            if (!item.getRankId().equals(ranking.getRankId())) {
                return false;
            }
            if (!isRankItem(ranking, item.getItemId())) {
                log.error("isAllInRankItem fail@seq:{}, rankId:{} taskId:{} itemId:{} not in rank item",
                        item.getRankId(), item.getTaskId(), item.getItemId());
                return false;
            }
        }
        return true;
    }

    /**
     * 获取指定业务指定时刻处于有效状态的活动
     * 1.状态要有效
     * 2.时间要有效
     */
    public HdztActivity getHdztActivity(long actId, Date time) {
        // 活动ID必须大于 0
        if (actId <= 0) {
            return null;
        }
        HdztActivity activity = cacheService.getHdztActivity(actId);
        Long status = activity.getStatus();
        long beginSeconds = DateUtil.getSeconds(activity.getBeginTime());
        long endSeconds = DateUtil.getSeconds(activity.getEndTime());
        long mySeconds = DateUtil.getSeconds(time);
        if (HdztConst.STATUS_VALID == status && mySeconds >= beginSeconds && mySeconds <= endSeconds) {
            log.info("getHdztActivity ok@actId:{}, time:{} -> activity:{}", actId, DateUtil.format(time), activity.getActName());
            return activity;
        }

        log.warn("getHdztActivity fail@actId:{}, time:{} -> activity:{}", actId, DateUtil.format(time), null);
        return null;
    }

    /**
     * 获取指定业务指定时刻处于有效状态的榜单
     * 1.状态要有效
     * 2.时间要有效
     */
    public List<RankingConfig> getRankingConfigs(long actId, Date time, boolean bCheckOther) {
        // 注意返回的map是有序的
        Map<Long, RankingConfig> harRankingMap = cacheService.getRankingConfigMap(actId);
        List<RankingConfig> list = Lists.newArrayList();
        long nowSeconds = time.getTime() / 1000;
        String currTimeStr = DateUtil.format(time, "HH:mm:ss");
        String currMinuteSecond = DateUtil.format(time, "mm:ss");
        for (long rankId : harRankingMap.keySet()) {
            boolean bNotTest = actId != HdztConst.TEST_ACTID || !HdztConst.isDebugRankId(rankId);
            if (HdztConst.LOCAL_DEBUG_FLAG && bNotTest) {
                continue;
            }

            // 检查是否在可累榜的时间槽中 - added by guoliping / 2023-01-06
            List<String> solt = cacheService.findRankingConfigExt(actId, rankId).getUpdate_ranking_slot_in_hour();
            if (!TimeKeyHelper.checkSoltInHour(solt, currMinuteSecond)) {
                continue;
            }

            RankingConfig ranking = harRankingMap.get(rankId);
            Long status = ranking.getStatus();
            Date begin = ranking.getCalcBeginTime();
            Date end = ranking.getCalcEndTime();
            if (isValidRankingConfig(nowSeconds, status, begin, end)) {
                if (TimeKeyHelper.checkTimeSplitPolicy(currTimeStr, ranking, bCheckOther)) {
                    list.add(ranking);
                }
            }
        }
        //log.info("getRankingConfigs ok@actId:{}, time:{}, size:{}", actId, DateUtil.format(time), list.size());
        return list;
    }


    private boolean isValidRankingConfig(long nowSeconds, Long status, Date begin, Date end) {
        return HdztConst.STATUS_VALID == status && nowSeconds >= begin.getTime() / 1000 && nowSeconds <= end.getTime() / 1000;
    }

    /**
     * 获取当前正在进行的阶段信息
     * 1.阶段分组不可为空
     * 2.存在有效的活动阶段分组设置
     * 3.当前是阶段时间
     */
    public RankingPhase getCurrentRankPhase(long actId, Date now, String phaseGroupCode) {
        if (StringUtil.isBlank(phaseGroupCode)) {
            return null;
        }

        RankingPhaseGroup harPhaseGroup = cacheService.getRankingPhaseGroup(actId, phaseGroupCode);
        if (harPhaseGroup == null || HdztConst.STATUS_VALID != harPhaseGroup.getStatus()) {
            log.warn("getCurrentRankPhase fail1@not find phase group, actId:{}, now:{}, phaseGroupCode:{}", actId,
                    DateUtil.format(now), phaseGroupCode);
            return null;
        }

        // 遍历找到第一个时间区间符合的阶段信息对象（配置人员必须自己保证不重叠！）
        long seconds = now.getTime() / 1000;
        Map<Long, RankingPhase> harPhaseMap = cacheService.getRankingPhaseMap(actId, phaseGroupCode);
        for (long phaseId : harPhaseMap.keySet()) {
            RankingPhase harPhase = harPhaseMap.get(phaseId);
            long begin = harPhase.getBeginTime().getTime() / 1000;
            long end = harPhase.getEndTime().getTime() / 1000;
            if (seconds >= begin && seconds <= end) {
                return harPhase;
            }
        }

        return null;
    }

    /**
     * 获取榜单计榜成员
     */
    public List<List<Long>> getRankMemberRoles(RankingConfig ranConf) {
        // 结果需要按 ranking_member.position 字段升序排好
        RankingMember harRankMember = cacheService.getRankingMember(ranConf.getActId(), ranConf.getRankId());
        if (harRankMember == null) {
            return null;
        }

        String roles = StringUtil.trim(harRankMember.getRoles());
        String[] ary = roles.split("\\|");
        if (ary == null || ary.length == 0) {
            return null;
        }

        List<List<Long>> lists = Lists.newArrayList();
        for (String elm : ary) {
            // 格式不合法，直接终止
            String[] strs = elm.split("&");
            if (strs == null || strs.length == 0) {
                return null;
            }

            List<Long> list = Lists.newArrayList();
            for (String str : strs) {
                list.add(Convert.toLong(str));
            }
            lists.add(list);
        }

        return lists;
    }

    /**
     * 获取阶段任务信息
     */
    public PhaseTaskInfo getPhaseTaskInfo(String actRankKey, RankingPhase phase, RankingConfig ranking, RankDataEvent dataEvent, Date now) {
        if (phase == null) {
            return null;
        }

        // 结果需要按 ranking_task.task_level 字段升序排好
        long actId = ranking.getActId();
        long rankId = ranking.getRankId();
        long phaseId = phase.getPhaseId();
        Map<Long, RankingTask> rankingTaskMap = cacheService.getRankingTaskMap(actId, rankId, phaseId);
        String actPhaseRankKey = KeyService.makeActPhaseRankKey(actRankKey, phaseId);

        // 看是否能做阶段任务
        String seq = dataEvent.getSeq();
        List<TaskLevelInfo> taskLevelInfos = this.getTaskLevelInfos(seq, actRankKey, ranking, rankingTaskMap, phaseId);

        PhaseTaskInfo phaseTaskInfo = new PhaseTaskInfo();
        phaseTaskInfo.setActId(actId);
        phaseTaskInfo.setRankId(rankId);
        phaseTaskInfo.setPhaseId(phaseId);

        // 设置本阶段的一些配置信息
        Map<String, RankingPhaseQualification> srcPhaseQualifications = cacheService.getRankingPhaseQualificationMapByDest(actId, rankId, phaseId);
        String actPhaseRankContestKey = CollectionUtils.isEmpty(srcPhaseQualifications) ? null : KeyService.makeActPhaseRankContestKey(actRankKey, phaseId);
        String actPhaseRankPromotKey = CollectionUtils.isEmpty(srcPhaseQualifications) ? null : KeyService.makeActPhaseRankPromotKey(actRankKey, phaseId);
        List<PhaseQualification> phaseQualifications = toPhaseQualifications(now, rankId, phaseId, actRankKey, srcPhaseQualifications);
        ActPhaseRankInfo actPhaseRankInfo = new ActPhaseRankInfo(actPhaseRankKey, actPhaseRankContestKey, actPhaseRankPromotKey, phaseQualifications);
        phaseTaskInfo.setActPhaseRankInfo(actPhaseRankInfo);

        // 填充下个阶段的一些配置信息
        //fillNextActPhaseInfoForRebuild(seq, actRankKey, phaseTaskInfo);

        String actPhaseTaskItemKey = getActPhaseTaskItemKey(taskLevelInfos, actRankKey, phaseId, dataEvent, rankId);
        phaseTaskInfo.setActPhaseTaskItemKey(actPhaseTaskItemKey);
        phaseTaskInfo.setRecycleCount(phase.getRecycleCount());
        phaseTaskInfo.setTaskLevelInfos(taskLevelInfos);
        phaseTaskInfo.setSkipPhaseUpdate(ranking.getSkipPhaseUpdate());
        phaseTaskInfo.setRecycleType(phase.getRecycleType());
        return phaseTaskInfo;
    }

    /**
     * 提取阶段任务礼物累积 key
     *
     * <AUTHOR>
     * @date 2020年7月8日 下午12:36:58
     */
    private String getActPhaseTaskItemKey(List<TaskLevelInfo> taskLevelInfos, String actRankKey, long phaseId, RankDataEvent dataEvent, long rankId) {
        if (CollectionUtils.isEmpty(taskLevelInfos)) {
            return null;
        }

        List<TaskLevelItemInfo> taskLevelItemInfos = taskLevelInfos.get(0).getTaskLevelItemInfos();
        if (CollectionUtils.isEmpty(taskLevelItemInfos)) {
            return null;
        }

        RankingItem rankingItem = cacheService.getRankingItem(dataEvent.getActId(), rankId);
        String itemAlias = StringUtil.trim(rankingItem.getItemAlias());

        String itemId = itemAlias.isEmpty() ? dataEvent.getItemId() : itemAlias;
        for (TaskLevelItemInfo tlii : taskLevelItemInfos) {
            if (itemId.equals(tlii.getItemId())) {
                return tlii.getPhaseItemValueKey();
            }
        }

        return null;
    }


    /**
     * 检查是否存在循环依赖
     *
     * <AUTHOR>
     * @date 2020年7月14日 下午12:56:22
     */
    private void checkRecycleDependence(Map<String, RankingPhaseQualification> qualifications) {
        for (String destkey : qualifications.keySet()) {
            RankingPhaseQualification destQualification = qualifications.get(destkey);
            String srcKey = destQualification.getSrcActId() + HdztConst.KEY_SEPERATOR + destQualification.getSrcRankId() + HdztConst.KEY_SEPERATOR + destQualification.getSrcPhaseId();
            if (destkey.equals(srcKey)) {
                throw new SuperException("存在自我依赖循环", 9999);
            }
            while (qualifications.containsKey(srcKey)) {
                RankingPhaseQualification nextQualification = qualifications.get(srcKey);
                srcKey = nextQualification.getSrcActId() + HdztConst.KEY_SEPERATOR + nextQualification.getSrcRankId() + HdztConst.KEY_SEPERATOR + nextQualification.getSrcPhaseId();
                if (destkey.equals(srcKey)) {
                    throw new SuperException("存在跨阶段的循环依赖", 9999);
                }
            }
        }
    }

    /**
     * 检查内部依赖的时间依赖是否正确
     *
     * <AUTHOR>
     * @date 2020年7月14日 下午12:56:22
     */
    private void checkTimeDependence(Map<String, RankingPhaseQualification> qualifications) {
        for (String destkey : qualifications.keySet()) {
            RankingPhaseQualification destQualification = qualifications.get(destkey);
            long srcActId = destQualification.getSrcActId();
            long srcRankId = destQualification.getSrcRankId();
            long srcPhaseId = destQualification.getSrcPhaseId();
            String srcKey = srcActId + HdztConst.KEY_SEPERATOR + srcRankId + HdztConst.KEY_SEPERATOR + srcPhaseId;
            if (qualifications.containsKey(srcKey)) {
                long destActId = destQualification.getDestActId();
                long destPhaseId = destQualification.getDestPhaseId();
                RankingPhase destRankingPhase = cacheService.getRankingPhase(destActId, destPhaseId);
                RankingPhase srcRankingPhase = cacheService.getRankingPhase(srcActId, srcPhaseId);
                long destBeginSeconds = destRankingPhase.getBeginTime().getTime() / 1000;
                long srcEndSeconds = srcRankingPhase.getEndTime().getTime() / 1000;
                if (srcEndSeconds >= destBeginSeconds) {
                    throw new SuperException("来源的结束时间不能晚于目标的开始时间！", 9999);
                }
            }
        }
    }


    /**
     * 精确到秒，检查时间是否有重合
     *
     * <AUTHOR>
     * @date 2020年7月14日 下午12:59:30
     */
    private void checkTimeCoincide(long myActId, long myRankId, long myPhaseId, Map<String, RankingPhaseQualification> qualifications) {
        RankingPhase myRankingPhase = cacheService.getRankingPhase(myActId, myPhaseId);
        long myEndTime = myRankingPhase.getEndTime().getTime() / 1000;
        for (String destKey : qualifications.keySet()) {
            RankingPhaseQualification qualification = qualifications.get(destKey);
            long destPhaseId = qualification.getDestPhaseId();
            long destActId = qualification.getDestActId();
            RankingPhase destRankingPhase = cacheService.getRankingPhase(destActId, destPhaseId);
            long destBeginTime = destRankingPhase.getBeginTime().getTime() / 1000;
            long diff = myEndTime - destBeginTime;
            if (diff > 0) {
                throw new SuperException("阶段之间有时间重合！", 9999);
            }
        }
    }

    /**
     * 找齐以本阶段榜单为晋级来源的所有直接下游 阶段
     *
     * <AUTHOR>
     * @date 2020年7月10日 上午11:19:21
     */
    public List<RankingPhaseQualification> getNextPromotPhases(long myActId, long myRankId, long myPhaseId) {
        // 1. 先找齐以本阶段榜为晋级来源的 RankingPhaseQualification 对象
        Map<String, RankingPhaseQualification> qualifications = cacheService.getRankingPhaseQualificationMapBySrc(myActId, myRankId, myPhaseId);

        List<RankingPhaseQualification> result = Lists.newArrayList();

        if (!CollectionUtils.isEmpty(qualifications)) {
            // 2. 检查  qualifications，确保时间依赖是正确的
            checkRecycleDependence(qualifications);

            // 3. 本阶段结束时间 和  直接下游阶段开始时间重合度检查（重合会导致频繁重建下游key，会有性能问题！）
            checkTimeCoincide(myActId, myRankId, myPhaseId, qualifications);

            // 4. 检查时间依赖
            checkTimeDependence(qualifications);

            // 5. 调整结果，确保阶段结束时间越早的排在前面，这样才能保证依赖阶段之间可以正确重建
            List<RankingPhase> list = getRankingPhaseList(qualifications);

            // 6. 先按排序了的 list 过滤一遍 qualifications
            for (RankingPhase phase : list) {
                Long phaseId = phase.getPhaseId();
                for (String destkey : qualifications.keySet()) {
                    RankingPhaseQualification qualification = qualifications.get(destkey);
                    if (Objects.equals(phaseId, qualification.getDestPhaseId())) {
                        result.add(qualification);
                    }
                }
            }

            // 7. 将还未放入 result 的 RankingPhaseQualification 加入到队列末尾
            for (String destkey : qualifications.keySet()) {
                RankingPhaseQualification qualification = qualifications.get(destkey);
                if (!result.contains(qualification)) {
                    result.add(qualification);
                }
            }
        }
        return result;
    }

    /**
     * 获取所有资格配置的阶段信息，按阶段结束时间排序，结束时间早的排前
     *
     * <AUTHOR>
     * @date 2020年7月14日 下午4:13:24
     */
    private List<RankingPhase> getRankingPhaseList(Map<String, RankingPhaseQualification> qualifications) {
        List<RankingPhase> list = Lists.newArrayList();
        for (String destkey : qualifications.keySet()) {
            RankingPhaseQualification destQualification = qualifications.get(destkey);
            long destPhaseId = destQualification.getDestPhaseId();
            long destActId = destQualification.getDestActId();
            RankingPhase rankingPhase = cacheService.getRankingPhase(destActId, destPhaseId);
            list.add(rankingPhase);
        }

        Collections.sort(list, new Comparator<RankingPhase>() {
            @Override
            public int compare(RankingPhase o1, RankingPhase o2) {
                // 精确到秒进行比较
                long t1 = o1.getEndTime().getTime() / 1000;
                long t2 = o2.getEndTime().getTime() / 1000;
                if (t1 > t2) {
                    return 1;
                } else if (t1 == t2) {
                    return 0;
                } else {
                    return -1;
                }
            }
        });

        return list;
    }

    /**
     * 看结算源阶段的 key 是否需要包含 .promot 和 .contest 后缀
     */
    private boolean noPromotSuffix(long actId, long rankId, long phaseId) {
        String content = cacheService.findRankingConfigExt(actId, rankId).getSrc_phases_no_promot_suffix();
        if (StringUtil.isBlank(content)) {
            return false;
        }
        List<Long> phaseIds = JSON.parseArray(content, Long.class);
        if (CollectionUtils.isEmpty(phaseIds)) {
            return false;
        }

        return phaseIds.contains(phaseId);
    }

    /**
     * 提取资格来源阶段榜单key列表
     * <p>
     * 注意：1）若资格来源有时间分榜的，是不能用作晋级资格来源的，
     * 2）资格来源的key会是错误的，因为使用的 destActRankKey 上的时间是当前时刻的，也就是当前阶段的！
     *
     * <AUTHOR>
     * @date 2020年6月14日 下午5:03:34
     */
    public List<PhaseQualification> toPhaseQualifications(Date now, long destRankId, long destPhaseId, String destActRankKey, Map<String, RankingPhaseQualification> srcPhaseQualifications) {
        List<PhaseQualification> result = Lists.newArrayList();
        if (!CollectionUtils.isEmpty(srcPhaseQualifications)) {
            for (String srcKey : srcPhaseQualifications.keySet()) {
                String[] strs = srcKey.split("\\" + HdztConst.KEY_SEPERATOR);
                long srcActId = Convert.toLong(strs[0]);
                long srcRankId = Convert.toLong(strs[1]);
                long srcPhaseId = Convert.toLong(strs[2]);
                if (srcRankId == destRankId && srcPhaseId == destPhaseId) {
                    throw new RankingException("晋级源榜单阶段和目标榜单阶段不能相同", RankingException.E);
                }
                // 若来源阶段本身没有经过资格晋级筛选，则取阶段原始榜单来做筛选
                RankingPhaseQualification hpq = srcPhaseQualifications.get(srcKey);
                long pkReviveCount = hpq.getPkReviveCount();
                Map<String, RankingPhaseQualification> srcSrcMap = cacheService.getRankingPhaseQualificationMapByDest(srcActId, srcRankId, srcPhaseId);
                boolean bNoPromotSuffix = CollectionUtils.isEmpty(srcSrcMap) || noPromotSuffix(srcActId, srcRankId, srcPhaseId);
                String contestKey = bNoPromotSuffix ? keyService.makeActPhaseRankKey(destActRankKey, srcActId, srcRankId, srcPhaseId) : keyService.makeActPhaseRankContestKey(destActRankKey, srcActId, srcRankId, srcPhaseId);
                String promotKey = bNoPromotSuffix ? keyService.makeActPhaseRankKey(destActRankKey, srcActId, srcRankId, srcPhaseId) : keyService.makeActPhaseRankPromotKey(destActRankKey, srcActId, srcRankId, srcPhaseId);
                String reviveKey = pkReviveCount > 0 ? keyService.makeActPhaseRankReviveKey(destActRankKey, srcActId, srcRankId, srcPhaseId) : null;

                // 在复活场景中，晋级lua中可根据该指示 key 获取原始分排名，用于定制逻辑 - added by guoliping / 2022-11-08
                String actPhaseRankPkKey = null;
                if (pkReviveCount > 0) {
                    Map<Long, RankingPhasePk> rankingPhasePks = cacheService.getRankingPhasePkByDest(srcActId, srcRankId, srcPhaseId);
                    if (!CollectionUtils.isEmpty(rankingPhasePks) && !rankingPhasePks.containsKey(srcRankId)) {
                        actPhaseRankPkKey = keyService.makeActPhaseRankPkKey(destActRankKey, srcActId, srcRankId, srcPhaseId);
                    }
                }

                // 准备源榜资格要求key
                String srcEntryListKey = null;

                if (cacheService.findRankingConfigExt(srcActId, srcRankId).getRace_qualification_flag() == 1) {
                    srcEntryListKey = KeyService.makeRankingEntrylistKey(srcActId, srcRankId);
                }

                // 准备目标榜资格要求key
                String destEntryListKey = null;
                if (cacheService.findRankingConfigExt(hpq.getDestActId(), hpq.getDestRankId()).getRace_qualification_flag() == 1) {
                    destEntryListKey = KeyService.makeRankingEntrylistKey(hpq.getDestActId(), hpq.getDestRankId());
                }

                // 准备晋级条件过滤, 先确保是正确的格式
                String filterConditions = StringUtil.trim(hpq.getFilterCondition());
                if (!filterConditions.isEmpty()) {
                    try {
                        JSONObject jo = JSON.parseObject(filterConditions);
                        if (StringUtil.isBlank(jo.getString("hashKey"))) {
                            jo.put("hashKey", srcEntryListKey);
                        }
                        filterConditions = jo.toJSONString();
                    } catch (Throwable t) {
                        log.error("toPhaseQualifications error@srcKey:{}, filter_condition:{}", filterConditions);
                        filterConditions = "";
                    }
                }

                // 准备PK结算key
                List<String> pkSettleKeys = makePkSettleKeyForPhaseQualification(srcActId, srcRankId, srcPhaseId);


                // 准备来源榜单自己的榜单KEY 和 来源榜当前的阶段榜单KEY (注意：这里假定榜单时最简单的情况！！！）
                String actRankKey = KeyService.makeActRankKey(srcActId, srcRankId);
                RankingConfig rankingConfig = cacheService.getRankingConfig(srcActId, srcRankId);
                String phaseGroupCode = rankingConfig.getPhaseGroupCode();
                RankingPhase phase = this.getCurrentRankPhase(srcActId, now, phaseGroupCode);
                String actPhaseRankKey = phase == null ? null : KeyService.makeActPhaseRankKey(actRankKey, phase.getPhaseId());
                long passOffset = Math.max(0, hpq.getPassOffset());

                PhaseQualification phaseQualification = new PhaseQualification(contestKey, hpq.getPkGroup(), promotKey,
                        hpq.getPassScore(), hpq.getPassCount(), hpq.getPassPolicy(), srcEntryListKey, destEntryListKey,
                        filterConditions, hpq.getExtjson(), pkSettleKeys, actRankKey, actPhaseRankKey, pkReviveCount, reviveKey, passOffset, actPhaseRankPkKey);

                result.add(phaseQualification);
            }
        }
        return result;
    }

    /**
     * 为资格来源阶段准备PK结算key, 若是有时间分榜，需要取最后一个时间分片的 PK结算key
     */
    private List<String> makePkSettleKeyForPhaseQualification(long actId, long rankId, long phaseId) {
        List<String> pkSettleKeys = Lists.newArrayList();
        Map<Long, RankingPhasePk> rankingPhasePks = cacheService.getRankingPhasePkByDest(actId, rankId, phaseId);

        if (!CollectionUtils.isEmpty(rankingPhasePks)) {
            for (Long srcRankId : rankingPhasePks.keySet()) {
                RankingPhasePk rankingPhasePk = rankingPhasePks.get(srcRankId);
                if (rankId == rankingPhasePk.getSrcRankId()) {
                    continue;
                }

                // 计算key PK来源阶段最后一个PK结算key（可用于晋级结算的前置条件检查）
                RankingConfig rankingConfig = cacheService.getRankingConfig(actId, srcRankId);
                if (rankingConfig == null) {
                    continue;
                }
                RankingPhase rankingPhase = cacheService.getRankingPhase(actId, phaseId);
                if (rankingPhase == null) {
                    continue;
                }
                Date endTime = rankingPhase.getEndTime();
                Date time = TimeKeyHelper.adjustTimeForPhaseSettle(rankingConfig, endTime, endTime, false);
                String actRankKey = PromotSettleService.makeZsetKeyForPromot(rankingConfig, time);
                if (StringUtil.isBlank(actRankKey)) {
                    continue;
                }
                String pkSettleKey = KeyService.makeActPhaseRankPkSettleKey(actRankKey, phaseId);
                pkSettleKeys.add(pkSettleKey);
            }
        }
        return pkSettleKeys;
    }

    /**
     * 获取过任务等级信息
     */
    public List<TaskLevelInfo> getTaskLevelInfos(String seq, String actRankKey, RankingConfig ranking, Map<Long, RankingTask> taskMap, long phaseId) {
        List<TaskLevelInfo> taskLevelInfos = Lists.newArrayList();
        if (!CollectionUtils.isEmpty(taskMap)) {
            List<TaskLevelItemInfo> prevTaskLevelItemInfos = null;
            for (Long taskId : taskMap.keySet()) {
                Long rankId = ranking.getRankId();
                RankingTask task = taskMap.get(taskId);
                Long actId = task.getActId();
                List<RankingTaskItem> taskItems = Lists.newArrayList(cacheService.getRankingTaskItemMap(actId, task.getRankId(), task.getPhaseId(), taskId).values());

                // 检查所有过任务项目在 计榜项目 表中是否都已登记
                if (!isAllInRankItem(ranking, taskItems)) {
                    //log.error("getTaskLevelInfos fail@task item not in rank:{} item, taskId:{}, seq:{}", rankId, taskId, seq);
                    break;
                }

                List<TaskLevelItemInfo> taskLevelItemInfos = this.makeTaskLevelItemInfos(actRankKey, rankId, taskItems, prevTaskLevelItemInfos, phaseId);

                TaskLevelInfo taskLevelInfo = new TaskLevelInfo();
                taskLevelInfo.setLevel(task.getTaskLevel());
                taskLevelInfo.setTaskId(taskId);
                taskLevelInfo.setTaskLevelItemInfos(taskLevelItemInfos);
                taskLevelInfos.add(taskLevelInfo);

                prevTaskLevelItemInfos = taskLevelItemInfos;
            }
        }
        return taskLevelInfos;
    }

    /**
     * 制作某个等级所有项目的过任务信息
     * 1.所有任务等级的过任务项目数必须相同
     * 2.所有任务等级的过任务项目标识 和 排列顺序必须相同
     */
    private List<TaskLevelItemInfo> makeTaskLevelItemInfos(String actRankKey, long rankId, List<RankingTaskItem> taskItems, List<TaskLevelItemInfo> prevTaskLevelItemInfos, long phaseId) {
        List<TaskLevelItemInfo> taskLevelItemInfos = Lists.newArrayList();
        if (CollectionUtils.isEmpty(taskItems)) {
            return taskLevelItemInfos;
        }

        // 所有任务等级的过任务项目数必须相同
        boolean bPrevEmpty = CollectionUtils.isEmpty(prevTaskLevelItemInfos);
        if (!bPrevEmpty && taskItems.size() != prevTaskLevelItemInfos.size()) {
            log.error("all task level must have same item for pass, actRankKey:{}, phaseId:{}", actRankKey, phaseId);
            throw new SuperException("所有任务等级项目 和 排列顺序必相同！", SuperException.E_CONF_ILLEGAL);
        }

        for (int i = 0; i < taskItems.size(); i++) {
            RankingTaskItem taskItem = taskItems.get(i);
            long passValue = taskItem.getPassValue();
            TaskLevelItemInfo prevTaskLevelItemInfo = bPrevEmpty ? null : prevTaskLevelItemInfos.get(i);

            // 所有任务等级的过任务项目标识 和 排列顺序必须相同
            String itemId = taskItem.getItemId();
            if (prevTaskLevelItemInfo != null && !itemId.equals(prevTaskLevelItemInfo.getItemId())) {
                log.error("all task level must have same item for pass, actRankKey:{}, phaseId:{}", actRankKey, phaseId);
                throw new SuperException("所有任务等级的项目标识 和 排列顺序必须相同！", SuperException.E_CONF_ILLEGAL);
            }

            String phaseItemValueKey = KeyService.makeActPhaseRankTaskItemKey(actRankKey, phaseId, itemId);
            long prevLevelTotalCount = prevTaskLevelItemInfo == null ? 0 : prevTaskLevelItemInfo.getTotalValue();

            TaskLevelItemInfo taskLevelItemInfo = new TaskLevelItemInfo();
            taskLevelItemInfo.setItemId(itemId);
            taskLevelItemInfo.setPassValue(passValue);
            taskLevelItemInfo.setTotalValue(prevLevelTotalCount + passValue);
            taskLevelItemInfo.setPhaseItemValueKey(phaseItemValueKey);
            taskLevelItemInfos.add(taskLevelItemInfo);
        }

        return taskLevelItemInfos;
    }

    /**
     * 调整角色到指定分组下(roleCounts/roleScores 中复合角色怎么处理？）
     */
    public void adjustRankDataEventActors(RankDataEvent dataEvent) {
        //log.info("adjustRankDataEventActors doing@dataEvent:{}", dataEvent);
        Map<Long, String> srcActors = dataEvent.getActors();
        if (CollectionUtils.isEmpty(srcActors)) {
            return;
        }

        // 存放发生替换的角色
        Map<String, String> replaceRoleMap = Maps.newHashMap();

        String seq = dataEvent.getSeq();
        long actId = dataEvent.getActId();
        Map<Long, HdztEnrollPolicy> enrollPolicyMap = cacheService.getHdztEnrollPolicyMap(actId);
        List<RankingEnrollment> autoEnrollList = Lists.newArrayList();
        Map<Long, String> destActors = Maps.newHashMap();
        for (Long srcRoleId : srcActors.keySet()) {
            // 若角色成员灰度白名单检查失败，则忽略被检查的角色
            HdztEnrollPolicy enrollPolicy = enrollPolicyMap == null ? null : enrollPolicyMap.get(srcRoleId);

            // 不存在或检查标记为0，则不做报名处理，用回上报本身的角色
            String memberId = srcActors.get(srcRoleId);
            if (enrollPolicy == null || enrollPolicy.getCheckFlag() == 0) {
                destActors.put(srcRoleId, memberId);
                continue;
            }

            // 要做报名处理, 并且已经存在报名信息，按转换标志使用角色
            EnrollmentInfo info = enrollmentService.getNoCache(actId, memberId, srcRoleId, true);
            if (info != null) {
                // 无条件使用找到的报名信息
                Long oldRoleId = (enrollPolicy.getConvertFlag() == 0) ? info.getSrcRoleId() : info.getDestRoleId();
                destActors.put(oldRoleId, memberId);

                // 保存发生替换的角色，用来替换 roleCounts、roleScores 中出现的角色ID
                if (!oldRoleId.equals(srcRoleId)) {
                    replaceRoleMap.put(srcRoleId.toString(), oldRoleId.toString());

                    // 对角色发生变化的，主动发如流通知，提醒相关人员介入, 异步执行
                    Const.EXECUTOR_GENERAL.execute(() -> notifyRoleChanged(dataEvent, memberId, oldRoleId, srcRoleId));
                }

                continue;
            }

            // 要做报名处理, 但没有报名信息， 且不允许通过, 则跳过 srcRoleId 角色
            if (enrollPolicy.getPassWay() == 0) {
                log.info("adjustRankDataEventActors ignore2@actId:{}, seq:{}, memberId:{}, srcRoleId:{}", actId, seq, memberId, srcRoleId);
                continue;
            }

            // 要做报名处理, 但没有报名信息， 且允许通过时，用回上报的角色
            destActors.put(srcRoleId, memberId);

            // 若需要自动保存，先收集，后面再异步入库
            if (info == null && enrollPolicy.getAutoEnroll() == 1) {
                RankingEnrollment enrollment = makeRankingEnrollment(seq, actId, srcRoleId, memberId, srcActors);
                autoEnrollList.add(enrollment);
            }
        }

        // 异步执行，不可抛出异常
        Const.EXECUTOR_GENERAL.execute(() -> saveAutoEnroll(autoEnrollList, seq));

        // 替换更新
        dataEvent.setActors(destActors);
        dataEvent.setRoleCounts(adjustRoleValue(dataEvent.getRoleCounts(), replaceRoleMap));
        dataEvent.setRoleScores(adjustRoleValue(dataEvent.getRoleScores(), replaceRoleMap));
    }

    /**
     * 对角色发生变化的，主动发如流通知，提醒相关人员介入
     */
    public void notifyRoleChanged(RankDataEvent dataEvent, String memberId, long oldRoleId, long srcRoleId) {
        if (srcRoleId == oldRoleId) {
            return;
        }

        long actId = dataEvent.getActId();
        long group = redisGroupService.getGroupCodeOld(actId);
        String key = Const.addActivityPrefix(actId, String.format("notifyRoleChanged:%s:%s:%s", memberId, oldRoleId, srcRoleId));

        String seq = dataEvent.getSeq();
        long busiId = dataEvent.getBusiId();

        try {
            // 非生产环境下，为防止活动虚拟时间持续如流消息干扰，活动开始后就不再处理测试环境的如流通知
            HdztActivity hdztActivity = cacheService.getHdztActivity(actId);
            long beginMills = hdztActivity.getBeginTime().getTime();
            if (!SysEvHelper.isDeploy() && System.currentTimeMillis() > beginMills) {
                return;
            }

            // 每个成员每 3 天通知一次（防止太频繁，干扰其它运维消息）
            int dayNum = 3;
            RedisTemplate<String, String> redisTemplate = rankingRedisDao.getRedisTemplate(group);
            if (redisTemplate.opsForValue().setIfAbsent(key, seq, dayNum, TimeUnit.DAYS)) {
                String actName = hdztActivity.getActName();
                HdztActor oldActor = cacheService.getHdztActor(oldRoleId);
                HdztActor srcActor = cacheService.getHdztActor(srcRoleId);
                HdztBusiness hdztBusiness = cacheService.gethdztBusiness(busiId);
                String oldName = oldActor == null ? "-" : oldActor.getName();
                String srcName = srcActor == null ? "-" : srcActor.getName();
                String busiName = hdztBusiness == null ? "-" : hdztBusiness.getName();
                StringBuffer buf = new StringBuffer("### <font color=\"red\">上报角色和中台首次记录不同</font>\n");
                buf.append("#### [" + actId + "]" + actName + "\n");
                buf.append("日   期：" + DateUtil.today("yyyy-MM-dd") + " (去重间隔3天)\n");
                buf.append("成员标识：" + memberId + "\n");
                buf.append("上报业务：[" + busiId + "]" + busiName + "\n");
                buf.append("上报角色：[" + srcRoleId + "]" + srcName + "\n");
                buf.append("中台角色：[" + oldRoleId + "]" + oldName + "\n");
                buf.append("请求SEQ:" + seq + "\n");
                baiduInfoFlowRobotService.sendNotifyByConfigKey(ParamName.IMGroup.IMG_IT_RUNNING_STATUS, buf.toString(), null);
            }

            log.info("notifyRoleChanged done@actId:{}, seq:{}, busiId:{}, memberId:{}, srcRoleId:{}, oldRoleId:{}",
                    actId, seq, busiId, memberId, srcRoleId, oldRoleId);
        } catch (Throwable t) {
            log.error("notifyRoleChanged exception@actId:{}, seq:{}, busiId:{}, memberId:{}, srcRoleId:{}, oldRoleId:{}, err:{}",
                    actId, seq, busiId, memberId, srcRoleId, oldRoleId, t.getMessage(), t);
        }
    }

    /**
     * 调整指定角色分值、数量的 key
     */
    private Map<String, Long> adjustRoleValue(Map<String, Long> oldRoleValueMap, Map<String, String> replaceRoleMap) {
        if (CollectionUtils.isEmpty(oldRoleValueMap) || CollectionUtils.isEmpty(replaceRoleMap)) {
            return oldRoleValueMap;
        }
        Map<String, Long> newRoleValueMap = Maps.newHashMap();
        for (String key : oldRoleValueMap.keySet()) {
            String[] roleIds = key.split("&");
            for (int i = 0; i < roleIds.length; i++) {
                String oldRoleId = roleIds[i];
                if (replaceRoleMap.containsKey(oldRoleId)) {
                    roleIds[i] = replaceRoleMap.get(oldRoleId);
                }
            }
            String newRoleValue = StringUtils.join(roleIds, "&");
            newRoleValueMap.put(newRoleValue, oldRoleValueMap.get(key));
        }
        return newRoleValueMap;
    }

    private String makeFlagKey(long actId, Long srcRoleId, String memberId) {
        return actId + "|" + srcRoleId + "|" + memberId;
    }

    /**
     * 制作报名对象
     */
    private RankingEnrollment makeRankingEnrollment(String seq, long actId, long roleId, String memberId, Map<Long, String> srcActors) {
        RankingEnrollment bean = new RankingEnrollment();
        bean.setActId(actId);
        bean.setMemberId(memberId);
        bean.setSrcRoleId(roleId);
        bean.setDestRoleId(roleId);
        bean.setStatus(1L);
        // 解决后面的 BeanUtils.copyProperties() null 错误！！！
        bean.setSignSid(0L);
        bean.setSignAsid(0L);
        bean.setRemark("auto enroll#" + DateUtil.today() + ", " + SystemUtil.getWorkerBrief() + ", seq:" + seq);

        // 搜出 srcActors 中的角色类型是公会的，将首个找到的公会类型角色值当做是签约公会
        for (Long key : srcActors.keySet()) {
            HdztActor hdztActor = cacheService.getHdztActor(key);
            if (hdztActor != null && hdztActor.getType() == RoleType.GUILD.getValue()) {
                Long sid = Convert.toLong(srcActors.get(key), -1);
                if (sid != -1) {
                    bean.setSignSid(sid);
                    // 为避免同步阻塞的性能问题，其它地方自己负责转短位号
                    bean.setSignAsid(sid);
                    break;
                }
            }
        }

        return bean;
    }

    /**
     * 本函数不可抛出异常
     */
    private void saveAutoEnroll(List<RankingEnrollment> autoEnrollList, String seq) {
        if (CollectionUtils.isEmpty(autoEnrollList)) {
            return;
        }
        Clock clock = new Clock();
        String flagkey = "";
        try {
            fillAsid(autoEnrollList);
            for (RankingEnrollment item : autoEnrollList) {
                flagkey = makeFlagKey(item.getActId(), item.getSrcRoleId(), item.getMemberId());
                try {
                    int result = enrollmentService.saveEnroll(item.getActId(), item, seq, false);
                    log.info("saveAutoEnroll ok@seq:{}, flagkey:{}, insert item:{},result:{}", seq, flagkey, item, result);
                } catch (Exception e) {
                    log.warn("saveAutoEnroll fail@seq:{}, flagkey:{}, item:{}, err:{}", seq, flagkey, item, e.getMessage(), e);
                }
            }

        } catch (Throwable t) {
            log.error("saveAutoEnroll exception@seq:{}, flagkey:{}, list:{}, err:{} {}", seq, flagkey, JSON.toJSONString(autoEnrollList), t.getMessage(), clock.tag(), t);
        }
    }

    /**
     * 填充频道短位
     */
    private void fillAsid(List<RankingEnrollment> autoEnrollList) {
        // 先找出所有合法的频道ID
        List<Long> sids = Lists.newArrayList();
        for (RankingEnrollment item : autoEnrollList) {
            Long signSid = item.getSignSid();
            if (signSid != null) {
                sids.add(signSid);
            }
        }
        if (CollectionUtils.isEmpty(sids)) {
            return;
        }

        // 查找、填充 asid
        Map<Long, Map<String, String>> maps = webdbThriftClient.batchGetChannelInfo(sids);
        if (!CollectionUtils.isEmpty(maps)) {
            for (RankingEnrollment item : autoEnrollList) {
                Long signSid = item.getSignSid();
                if (signSid != null) {
                    Map<String, String> map = maps.get(signSid);
                    if (map != null) {
                        long asid = Convert.toLong(map.get("asid"), signSid);
                        item.setSignAsid(asid);
                    }
                }
            }
        }
    }

    /**
     * 检查是否在白名单中，不是灰度状态时总是有效
     */
    public boolean checkWhiteList(long actId, long roleType, String memberId, Map<String, String> extData) {
        // 不在灰度状态总是返回 true
        if (!this.isActivityGrey(actId)) {
            return true;
        }

        //因约战PKID是玩法产生的动态值，对PKID角色的权限验证总是返回 true
        long yzPkRoleType = 601L;
        if (yzPkRoleType == roleType) {
            return true;
        }
        //检查系统配置里面的白名单
       /* if (isWhiteListFromParameter(roleType, memberId)) {
            return true;
        }*/
        // 在灰度状态才实际检查白名单
        String key = String.format(HdztConst.WHITE_LIST_KEY_TEMPLATE, actId, roleType);
        HdztActivity hdztActivity = cacheService.getHdztActivity(actId);
        long group = hdztActivity == null ? HdztConst.REDIS_BASE_GROUP : redisGroupService.getGroupCode(actId, RedisGroupService.RANK_ID_NON);
        return rankingRedisDao.sIsMember(group, key, memberId);
    }

    /**
     * 从配置中获取角色类型白名单 ，在<w{roleId}></w{roleId}> 标签下面
     *
     * @param roleType
     * @param memberId
     * @return
     */
    private boolean isWhiteListFromParameter(long roleType, String memberId) {
        Set<String> whiteList = roleWhiteListMap.get(roleType);
        try {
            if (whiteList == null) {
                whiteList = Sets.newHashSet();
                String roleTypeParamName = String.format("root.white_list.w%d", roleType);
                String whiteLists = Const.PM.getXmlFileElementValue(roleTypeParamName);
                String[] strs = whiteLists.split("\\n");
                for (String str : strs) {
                    str = StringUtil.trim(str);
                    if (!str.isEmpty()) {
                        whiteList.add(StringUtil.trim(str));
                    }
                }
                roleWhiteListMap.putIfAbsent(roleType, whiteList);
            }
        } catch (Exception e) {
            log.error("isWhiteListFromParameter error@role:{} memberId:{} {} ", roleType, memberId, e.getMessage(), e);
        }
        return whiteList.contains(memberId.trim());
    }

    /**
     * 检查 actors 中所有角色是否都在灰度白名单中，返回 true：全部在， false：有不在的
     */
    public boolean checkUpdateRanking(RankDataEvent dataEvent) {
        long actId = dataEvent.getActId();
        // 不在灰度状态总是返回 true
        if (!this.isActivityGrey(actId)) {
            return true;
        }

        Map<Long, String> actors = dataEvent.getActors();
        for (Long roleId : actors.keySet()) {
            String memberId = actors.get(roleId);
            long roleType = cacheService.getHdztActor(roleId).getType();
            if (!this.checkWhiteList(actId, roleType, memberId, null)) {
                log.warn("checkWhiteLists fail@actId:{}, seq:{}, roleId:{}, roleType:{}, memberId:{} not in whitelist", actId, dataEvent.getSeq(), roleId, roleType, memberId);
                sendNotice(actId, roleType, roleId, memberId, dataEvent.getItemId());
                return false;
            }
        }
        return true;
    }

    public void sendNotice(long actId, long roleType, Long roleId, String memberId, String itemId) {
        if (!SysEvHelper.isDeploy()) {
            return;
        }

        if (cacheService.findActivityConfigExt(actId).getOpen_white_list_notify() == 0) {
            log.info("sendNotice white list notify is not open actId={}", actId);
            return;
        }

        String ignoreItems = cacheService.findActivityConfigExt(actId).getIgnore_white_list_notify_items();
        if (StringUtils.isNotEmpty(ignoreItems) && ignoreItems.contains(itemId)) {
            return;
        }

        StringBuilder message = new StringBuilder();
        message.append("线上灰度白名单缺失告警\n");
        message.append("活动id = ").append(actId).append("\n");
        message.append("roleType = ").append(roleType).append("\n");
        message.append("roleId = ").append(roleId).append("\n");
        message.append("memberId = ").append(memberId).append("\n");

        baiduInfoFlowRobotService.sendNotifyByConfigKey(ParamName.IMGroup.IMG_IT_RUNNING_STATUS, message.toString(), new ArrayList<>());
    }

    /**
     * 设置活动级别的hash
     *
     * @param busiId
     * @param actId
     * @param data
     * @param sign
     * @return
     */
    public SimpleResult doSetActHash(long busiId, long actId, Map<String, String> data, String sign) {

        try {
            // 提取seq
            String seq = data == null ? "" : StringUtil.trim(data.get("seq"));
            if (seq.isEmpty()) {
                return new SimpleResult(-3, "seq is empty!", data);
            }
            String key = StringUtil.trim(data.get("key"));
            if (StringUtils.isBlank(key)) {
                return new SimpleResult(-1, "wrong parameter!key is black", data);
            }
            Map<String, String> mapData = JSONObject.parseObject(data.get("mapData"), new TypeReference<Map<String, String>>() {
            });
            if (CollectionUtils.isEmpty(mapData)) {
                return new SimpleResult(-1, "wrong parameter!mapData is empty", data);
            }

            // 检查活动有效性
            Date currSysTime = this.getCurrSysTime(actId);
            HdztActivity hdztActivity = this.getHdztActivity(actId, currSysTime);
            if (hdztActivity == null) {
                return new SimpleResult(-2, "invalid activity:" + actId, data);
            }

            String regex = ",";
            String allowSetKeys = cacheService.findActivityConfigExt(actId).getAllowSetKeys();
            if (!ArrayUtils.contains(allowSetKeys.split(regex), key)) {
                return new SimpleResult(-1, "key not allow set!", data);
            }

            long group = redisGroupService.getGroupCodeOld(actId);

            // 设置防重复标记
            String hashKey = Const.addActivityPrefix(actId, "doSetActHash");
            if (!rankingRedisDao.hsetnx(group, hashKey, seq, DateUtil.format(currSysTime))) {
                log.warn("doSetActHash skip2@duplicated request! busiId:{}, actId:{}, seq:{}, sign:{},", busiId, actId, seq, sign);
                return new SimpleResult(1, "duplicated request!", data);
            }

            rankingRedisDao.getRedisTemplate(group).opsForHash().putAll(key, mapData);


            return new SimpleResult(0, "success", data);
        } catch (Throwable t) {
            log.error("doSetActHash exception@busiId:{}, actId:{}, data:{}, sign:{}, err:{}", busiId, actId, JSONObject.toJSONString(data), sign, t.getMessage(), t);
            return new SimpleResult(-99, t.getMessage(), data);
        }

    }

    /**
     * 对榜单更新做完全定制的处理，本函数不抛出异常，出错不影响流程
     * （这是一个定制函数，活动完了应该取消！！！ - added by guoliping / 2020-11-13)
     */
    public SimpleResult doSelfDefProcess(long busiId, long actId, Map<String, String> data, String sign) {
        if (SysEvHelper.checkWriteForHistory("doSelfDefProcess", data, false)) {
            return new SimpleResult(-99, "历史环境不允许写入操作", data);
        }
        long koBusiId = 200;
        // 陪玩活动ID
        long koActId = 202012002L;
        String koItem = "PW_KO_ITEM";
        // 约定 陪玩团 赛的榜单ID， 这个不能再变化！！！！
        long koRankId = 21;
        // 约定 陪玩团 赛 KO 阶段ID， 这个不能再变化！！！！
        long koPhaseId = 25;

        String flagStr = "DEST";
        if (data.containsKey(flagStr)) {
            String dest = data.get(flagStr);
            try {
                JSONObject destMap = JSON.parseObject(dest);
                koActId = destMap.getLongValue("koActId");
                koRankId = destMap.getLongValue("koRankId");
                koPhaseId = destMap.getLongValue("koPhaseId");
            } catch (Exception ex) {
                log.error("doSelfDefProcess exception@busiId:{}, actId:{}, data:{}, sign:{}, err:{}", busiId, actId, dest, sign, ex.getMessage(), ex);
            }
            data.remove(flagStr);
        }

        String content = JSON.toJSONString(data);

        try {
            // 功能要求条件检查， 和调用方约定的（侠专 中控）
            if (busiId != koBusiId || actId != koActId || !sign.equals(koItem)) {
                return new SimpleResult(-1, "wrong parameter!", data);
            }

            // 检查活动有效性
            Date currSysTime = this.getCurrSysTime(actId);
            HdztActivity hdztActivity = this.getHdztActivity(actId, currSysTime);
            if (hdztActivity == null) {
                return new SimpleResult(-2, "invalid activity:" + actId, data);
            }

            // 提取seq
            String seq = data == null ? "" : StringUtil.trim(data.get("seq"));
            if (seq.isEmpty()) {
                return new SimpleResult(-3, "seq is empty!", data);
            }

            // 防止意外提前生成 actPhaseRankKey 导致结算异常！
            long group = redisGroupService.getGroupCodeOld(actId);
            String actRankKey = KeyService.makeActRankKey(actId, koRankId);
            String actPhaseRankKey = KeyService.makeActPhaseRankContestKey(actRankKey, koPhaseId);
            if (!rankingRedisDao.getRedisTemplate(group).hasKey(actPhaseRankKey)) {
                log.warn("doSelfDefProcess skip1@key:{} not exists now, busiId:{}, actId:{}, data:{}, sign:{},", actPhaseRankKey, busiId, actId, content, sign);
                return new SimpleResult(-4, "key:" + actPhaseRankKey + " not exists now!", data);
            }

            // 设置防重复标记
            String splitChar = "|";
            String hashKey = Const.addActivityPrefix(actId, "200-" + sign);
            if (!rankingRedisDao.hsetnx(group, hashKey, seq, content + splitChar + DateUtil.format(currSysTime))) {
                log.warn("doSelfDefProcess skip2@duplicated request! busiId:{}, actId:{}, data:{}, sign:{},", busiId, actId, content, sign);
                return new SimpleResult(1, "duplicated request!", data);
            }

            String member = StringUtil.trim(data.get("member"));
            long score = Convert.toLong(data.get("score"));
            long newScore = rankingRedisDao.zIncrWithsuffix(group, actPhaseRankKey, member, score).longValue();
            log.info("doSelfDefProcess ok@busiId:{}, actId:{}, data:{}, sign:{} -> {}", busiId, actId, content, sign, newScore);

            return new SimpleResult(0, String.valueOf(newScore), data);
        } catch (Throwable t) {
            log.error("doSelfDefProcess exception@busiId:{}, actId:{}, data:{}, sign:{}, err:{}", busiId, actId, content, sign, t.getMessage(), t);
            return new SimpleResult(-99, t.getMessage(), data);
        }
    }

    /**
     * 写死了只查询活动id为2021114001,rankId为1501，phaseId为50
     * hdzt_ranking:2022047001:1:_:2022051610:_@cpTopInfo
     **/
    public SimpleResult queryUniqCp(Map<String, String> data, boolean useingRW) {
        try {
            Clock clock = new Clock();
            String actIdStr = data.remove("actId");
            String rankIdStr = data.remove("rankId");
            String dayStr = data.remove("dayStr");
            String phaseId = data.remove("phaseId");

            // 指定用户
            String pointMember = data.remove("pointMember");
            if (StringUtil.isEmpty(dayStr)) {
                dayStr = "_";
            }
            if (StringUtil.isEmpty(phaseId)) {
                phaseId = "_";
            }
            long actId = Convert.toLong(actIdStr, 0);
            long rankId = Convert.toLong(rankIdStr, 0);
            String redisKey = String.format("hdzt_ranking:%s:%s:_:%s:_|%s@cpTopInfo", actIdStr, rankIdStr, dayStr, phaseId);
            if (MapUtils.isEmpty(data)) {
                return new SimpleResult(0, "success", data);
            }
            List<Object> hashFields = data.keySet().stream().map(key -> "partner." + key).collect(Collectors.toList());
            long group = redisGroupService.getGroupCode(actId, rankId);
            clock.tag();
            List<Object> results = rankingRedisDao.hmGet(group, redisKey, hashFields, useingRW);
            clock.tag();

            String scoreKey = String.format("hdzt_ranking:%s:%s:_:%s:_|%s@cpAll", actIdStr, rankIdStr, dayStr, rankIdStr);
            List<String> scoreKeys = new ArrayList<>(results.size());
            List<String> scoreFields = new ArrayList<>();
            results.forEach(key -> {
                scoreKeys.add(scoreKey);
                scoreFields.add(key + "");
            });
            if (StringUtils.isNotEmpty(pointMember)) {
                scoreKeys.add(scoreKey);
                scoreFields.add(pointMember);
            }

            List<Object> scoreResults = rankingRedisDao.zScoreBatchKey(group, scoreKeys, scoreFields, null, useingRW);
            clock.tag();

            for (int index = 0; index < hashFields.size(); index++) {
                String hashField = (hashFields.get(index) + "").replace("partner.", "");
                if (results.get(index) != null) {
                    data.put(hashField, results.get(index) + "-" + Convert.toLong(scoreResults.get(index)));
                } else {
                    data.remove(hashField);
                }
            }
            if (scoreResults.size() == hashFields.size() + 1) {
                long score = 0;
                if (scoreResults.get(scoreResults.size() - 1) != null) {
                    score = Convert.toLong(scoreResults.get(scoreResults.size() - 1));
                }
                data.put("pointMember", pointMember + "-" + score);
            }

            log.info("queryUniqCp end {}", clock.tag());
        } catch (Exception ex) {
            return new SimpleResult(-99, ex.getMessage(), data);
        }

        return new SimpleResult(0, "success", data);
    }

    public SimpleResult queryUniqCpList(Map<String, String> data) {
        try {
            Clock clock = new Clock();
            String actIdStr = data.remove("actId");
            String rankIdStr = data.remove("rankId");
            String phaseId = data.remove("phaseId");
            if (MapUtils.isEmpty(data)) {
                return new SimpleResult(0, "success", data);
            }

            if (StringUtil.isEmpty(phaseId)) {
                phaseId = "_";
            }

            long actId = Convert.toLong(actIdStr, 0);
            long rankId = Convert.toLong(rankIdStr, 0);
            long group = redisGroupService.getGroupCode(actId, rankId);
            clock.tag();
            Map<String, String> redisKeyMap = Maps.newHashMap();
            Map<String, String> hashFieldMap = Maps.newHashMap();
            Map<String, String> scoreKeyMap = Maps.newHashMap();
            for (Map.Entry<String, String> entry : data.entrySet()) {
                // hdzt_ranking:%s:%s:_:%s:_|%s@cpTopInfo
                String redisKey = String.format("hdzt_ranking:%s:%s:_:%s:_|%s@cpTopInfo", actIdStr, rankIdStr, entry.getKey(), phaseId);
                redisKeyMap.put(entry.getKey(), redisKey);
                hashFieldMap.put(entry.getKey(), "partner." + entry.getValue());

                // hdzt_ranking:%s:%s:_:%s:_|%s@cpAll
                scoreKeyMap.put(entry.getKey(), String.format("hdzt_ranking:%s:%s:_:%s:_|%s@cpAll", actIdStr, rankIdStr, entry.getKey(), phaseId));
            }

            List<Object> results = rankingRedisDao.hgetBatchKey(group, new ArrayList<>(redisKeyMap.values()), new ArrayList<>(hashFieldMap.values()));
            clock.tag();

            List<String> scoreKeys = new ArrayList<>(scoreKeyMap.values());
            List<String> scoreFields = new ArrayList<>();
            results.forEach(key -> {
                scoreFields.add(new String((byte[]) key, StandardCharsets.UTF_8));
            });

            List<Object> scoreResults = rankingRedisDao.zScoreBatchKey(group, scoreKeys, scoreFields, null);
            clock.tag();
            List<Map.Entry<String, String>> hashFieldEntryList = new ArrayList<>(hashFieldMap.entrySet());
            for (int index = 0; index < hashFieldMap.size(); index++) {
                String hashField = (hashFieldEntryList.get(index).getValue()).replace("partner.", "");
                if (results.get(index) != null) {
                    data.put(hashFieldEntryList.get(index).getKey(), scoreFields.get(index) + "-" + Convert.toLong(scoreResults.get(index)));
                } else {
                    data.remove(hashField);
                }
            }
            log.info("queryUniqCpList end {}", clock.tag());
        } catch (Exception ex) {
            return new SimpleResult(-99, ex.getMessage(), data);
        }

        return new SimpleResult(0, "success", data);
    }

    public SimpleResult queryUniqPointedCp(Map<String, String> data) {
        Long actId = MapUtils.getLong(data, "actId", 0L);
        Long rankId = MapUtils.getLong(data, "rankId", 0L);
        String phaseId = MapUtils.getString(data, "phaseId");
        if (StringUtils.isEmpty(phaseId) || "0".equals(phaseId)) {
            phaseId = "_";
        }
        String dateStr = MapUtils.getString(data, "dateStr");
        if (StringUtils.isEmpty(dateStr)) {
            dateStr = "_";
        }
        String findSrcMembersStr = MapUtils.getString(data, "findSrcMembers", "_");

        String pointMember = MapUtils.getString(data, "pointMember", StringUtils.EMPTY);

        List<Long> uniqRankIds = cacheService.findRankingConfigExt(actId, rankId).getCp_uniq_rank_ids();
        if (CollectionUtils.isEmpty(uniqRankIds)) {
            return new SimpleResult(500, "invalid config!", null);
        }

        long uniqIdx1RankId = uniqRankIds.get(0), uniqIdx2RankId = uniqRankIds.get(0);
        if (uniqRankIds.size() > 1) {
            uniqIdx2RankId = uniqRankIds.get(1);
        }

        String key1 = String.format("hdzt_ranking:%d:%d:_:%s:%s|%s", actId, uniqIdx1RankId, dateStr, findSrcMembersStr, phaseId);
        String key2 = String.format("hdzt_ranking:%d:%d:_:%s:%s|%s", actId, uniqIdx2RankId, dateStr, findSrcMembersStr, phaseId);
        String key3 = String.format("hdzt_ranking:%d:%d:_:%s:%s|%s", actId, rankId, dateStr, findSrcMembersStr, phaseId);
        List<String> keys = ImmutableList.of(key1, key2, key3);
        List<String> argv = Collections.singletonList(pointMember);

        long group = redisGroupService.getGroupCode(actId, rankId);
        final String rs;
        try {
            rs = rankingRedisDao.executeLua(group, "rank/getCpUniqRankScoreGap.lua", String.class, keys, argv);
        } catch (Exception e) {
            log.error("executeLua getCpUniqRankScoreGap fail:", e);
            return new SimpleResult(500, e.getMessage(), Collections.emptyMap());
        }

        JSONObject json = JSON.parseObject(rs);
        Map<String, String> result = json.toJavaObject(new TypeReference<HashMap<String, String>>(){});
        return new SimpleResult(0, "success", result);
    }

    /**
     * 设置活动为归档状态
     */
    public void setActStatusArchive(long actId) {
        HdztActivity activity = cacheService.getHdztActivity(actId);
        Assert.isTrue(System.currentTimeMillis() > activity.getEndTime().getTime(), "活动未结束");
        Assert.isTrue(!ActStatus.ARCHIVE.equals(activity.getStatus()), "活动已经是归档状态");

        HdztActivity where = new HdztActivity();
        where.setActId(actId);
        HdztActivity update = new HdztActivity();
        update.setStatus(ActStatus.ARCHIVE);
        int result = rankingMysqlDao.update(HdztActivity.class, where, update);
        Assert.isTrue(result > Const.ZERO, "更新状态失败");

        log.info("set Archive done ,actId:{}", actId);
    }
}
